#!/usr/bin/env python3

import arrow
import click
from loguru import logger
import pandas as pd

from src.infrastructure.display import display_df, display_stats
from src.infrastructure.repo import update_rjo_agg_data


SUMMARY_COLS = [
    "Record_Type",
    "Record_Date",
    "Account_Number",
    "Account_Name",
    "Contract",
    "Market",
    "Trade_Date",
    "Trade_Month",
    "Quantity",
    "Commission",
    "Net_Commission",
]

GROUPBY_COLS = [
    "Record_Type",
    "Account_Number",
    "Account_Name",
    "Contract",
    "Market",
]

DAY_COLS = ["Trade_Date"]

MONTH_COLS = ["Trade_Month"]


def group_stats_df(df, by_day=False):
    groupby_cols = DAY_COLS + GROUPBY_COLS if by_day else MONTH_COLS + GROUPBY_COLS
    grouped = df.groupby(groupby_cols).agg(
        {"Commission": ["sum"], "Quantity": ["sum"], "Net_Commission": ["sum"]}
    )
    grouped = grouped.reset_index()
    grouped.columns = grouped.columns.get_level_values(0)
    # grouped = grouped[grouped["Record_Type"].isin(["A", "B", "E", "Q", "S", "T", "X"])]
    return grouped


def calculate_commission_rate(row):
    return round(row.Commission / row.Quantity, 2) if row.Quantity else 0.0


data = update_rjo_agg_data(2024)
print("base columns: ", data.columns)
# data22 = get_rjo_agg_data(2022)
# data21 = get_rjo_agg_data(2021)
# data = pd.concat([data, data22, data21], axis=0)
# data = data[data['Account_Name'] == 'LASSUS BROTHERS OIL']
data = group_stats_df(data, by_day=False).sort_values(by=["Account_Number", "Contract"])
data["Comm_Rate"] = data.apply(calculate_commission_rate, axis=1)
data = data[data["Trade_Month"] == 202402]
data.to_csv("data/2024.02_recon_data.csv")

# display_df(data, title='Lassus')
print(data.head)
