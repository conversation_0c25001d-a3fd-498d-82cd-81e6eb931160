from assertpy import assert_that  # type: ignore
import pandas as pd
import pytest

from src.constants import NYMEX, ICE, RJO_HEADERS, TRADE_DATA_FIELDS
from src.xdomain.etl import rjo
from src.xdomain.etl.rjo.actions import (
    clearing_cost_for_date_acct_mkt,
    commission_from_row,
    contract_type_from_row,
    net_commission_from_row,
    trade_market_from_row,
)


@pytest.fixture
def raw_rjo_dataframe():
    return pd.read_csv("./data/valid_file.csv", names=RJO_HEADERS, engine="python")


@pytest.fixture
def igs_4_acct():
    return 84478


@pytest.fixture
def igs_5_acct():
    return 85478


@pytest.fixture
def wrangler_acct():
    return 84663


def extracts_trade_data_from_raw_file_test(raw_rjo_dataframe):
    result = rjo.extract(raw_rjo_dataframe)

    assert_that(isinstance(result, pd.DataFrame)).is_true()
    assert_that(result.columns.tolist()).contains_only(
        *rjo.constants.EXTRACTED_DATA_FIELDS
    )
    assert_that(len(result)).is_greater_than(0)


def transforms_trade_data_test(raw_rjo_dataframe):
    trade_data = rjo.extract(raw_rjo_dataframe)
    result = rjo.transform(trade_data)

    assert_that(isinstance(result, pd.DataFrame)).is_true()
    assert_that(result.columns.tolist()).contains_only(*rjo.constants.TRADE_DATA_FIELDS)
    # check that P&S and Cash transactions have been removed
    assert_that(len(result)).is_less_than(len(raw_rjo_dataframe))
    assert_that(len(result[result["Record_Type"].isin(["C", "P"])])).is_equal_to(0)
    # check commission and net_commission calculations
    assert_that(result["Commission"].sum()).is_equal_to(4653.25)
    assert_that(round(result["Net_Commission"].sum(), 2)).is_equal_to(4428.85)


def aggregate_trades_test(raw_rjo_dataframe):
    trade_data = rjo.extract(raw_rjo_dataframe)
    trades = rjo.transform(trade_data)
    result = rjo.aggregate(trades)

    assert_that(isinstance(result, pd.DataFrame)).is_true()
    assert_that(result.columns.tolist()).contains_only(*rjo.constants.TRADE_DATA_FIELDS)
    assert_that(len(result)).is_greater_than(0)
    assert_that(len(result)).is_less_than(len(trades))
    assert_that(result["Commission"].sum()).is_equal_to(4653.25)
    assert_that(round(result["Net_Commission"].sum(), 2)).is_equal_to(4428.85)
