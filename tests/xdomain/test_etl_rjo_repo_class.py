from assertpy import assert_that  # type: ignore
import pandas as pd
import pytest

from string import Template
from src.xdomain.etl.rjo.constants import TRADE_DATA_FIELDS, RJO_HEADERS
from src.xdomain.etl.rjo.repo import RJORepo as Repo


@pytest.fixture
def test_directory_template():
    return Template("./tests/data/$year")


@pytest.fixture
def empty_agg_filename():
    return "empty_agg_file.csv"


@pytest.fixture
def empty_daily_filename():
    return "POWERHOUSE_csvth1_dth1_$year$month$day.csv"


@pytest.fixture
def test_agg_file_template():
    return Template("$year Test Agg Trade Data.csv")


@pytest.fixture
def test_daily_file_template():
    return Template("$year$month$day.csv")


@pytest.fixture
def test_repo():
    return Repo(
        data_dir_template=test_directory_template,
        daily_filename_template=test_daily_file_template,
        agg_filename_template=test_agg_file_template,
    )


def instantiates_a_repo_test():
    result = Repo()

    assert_that(result).is_not_none()
    assert_that(isinstance(result, Repo)).is_true()


def instantiates_a_repo_w_file_and_dir_templates_test(
    test_directory_template, test_daily_file_template, test_agg_file_template
):
    result = Repo(
        data_dir_template=test_directory_template,
        daily_filename_template=test_daily_file_template,
        agg_filename_template=test_agg_file_template,
    )

    assert_that(result).is_not_none()
    assert_that(isinstance(result, Repo)).is_true()


def throws_error_on_repo_update_earlier_than_2013_test(test_repo):
    assert_that(test_repo.update_stats).raises(ValueError).when_called_with(2012)


def loads_jan_31_2024_agg_data_test(test_repo):
    repo = Repo(
        data_dir_template=Template("./tests/data/$year/01.30"),
        daily_filename_template=Template("POWERHOUSE_csvth1_dth1_$year$month$day.csv"),
        agg_filename_template=Template("$year Test Agg Trade Data.csv"),
    )
    data = repo.get_day_summary(2024, 1, 31)

    assert_that(isinstance(data, pd.DataFrame)).is_true()
    assert_that(data.empty).is_false()
    assert_that(data.columns.tolist()).contains_only(*TRADE_DATA_FIELDS)
    assert_that(len(data)).is_greater_than(0)
    assert_that(round(data["Commission"].sum(), 2)).is_equal_to(4653.25)
    assert_that(round(data["Net_Commission"].sum(), 2)).is_equal_to(4428.85)


def saves_jan_1_1_agg_data_from_day_summary_test(test_repo):
    repo = Repo(
        data_dir_template=Template("./tests/data/$year"),
        daily_filename_template=Template("POWERHOUSE_csvth1_dth1_$year$month$day.csv"),
        agg_dir_template=Template("./tests/data/$year"),
        agg_filename_template=Template("$year Test Agg Trade Data.csv"),
    )
    data = repo.get_day_summary(2024, 1, 1)
    stats_dir = repo.agg_dir_template.substitute(year=2024)
    data_file = repo.agg_filename_template.substitute(year=2024)
    filename = f"{stats_dir}/{data_file}"
    repo.save(data, filename)
    stats = repo.get_trade_stats(2024, 1, 1)

    assert_that(isinstance(stats, pd.DataFrame)).is_true()
    assert_that(stats.empty).is_false()
    assert_that(round(stats["Commission"].sum(), 2)).is_equal_to(2799.50)
    assert_that(round(stats["Net_Commission"].sum(), 2)).is_equal_to(2706.85)
    assert_that(stats["Quantity"].sum()).is_equal_to(109)


def returns_right_files_since_date_test(test_repo):
    repo = Repo(
        data_dir_template=Template("./tests/data/$year"),
    )
    files = repo._daily_files_since(2024, 1, 29)

    assert_that(isinstance(files, list)).is_true()
    assert_that(len(files)).is_equal_to(2)
    assert_that(files[0]).contains("POWERHOUSE_csvth1_dth1_20240130.csv")
    assert_that(files[-1]).contains("POWERHOUSE_csvth1_dth1_20240131.csv")


def returns_empty_df_if_no_agg_file_exists_test():
    repo = Repo(
        data_dir_template=Template("./tests/data/$year"),
        agg_filename_template=Template("$year Missing File.csv"),
    )
    data = repo._read_agg_data(2024)

    assert_that(isinstance(data, pd.DataFrame)).is_true()
    assert_that(data.empty).is_true()
    assert_that(data.columns.tolist()).contains_only(*TRADE_DATA_FIELDS)


def returns_updated_stats_to_date_test():
    repo = Repo(
        data_dir_template=Template("./tests/data/$year"),
        agg_dir_template=Template("./tests/data/temp/$year"),
    )

    stats = repo.update_stats(2024)

    assert_that(isinstance(stats, pd.DataFrame)).is_true()
    assert_that(stats.empty).is_false()
