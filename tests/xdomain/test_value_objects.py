from assertpy import assert_that, fail  # type: ignore

from src.xdomain.value_objects import Symbol


def pytest_smoke_test():
    """Smoke test for pytest."""
    pass


def assertpy_smoke_test():
    assert_that(1).is_equal_to(1)


def futures_symbol_init_test():
    symbol = Symbol(contract="HO", month=1, year=2020)

    assert_that(str(symbol)).is_equal_to("HOF20")


def futures_symbol_w_cme_month_test():
    symbol = Symbol(contract="HO", month="F", year=2020)

    assert_that(str(symbol)).is_equal_to("HOF20")


def futures_symbol_w_bad_month_test():
    try:
        symbol = Symbol(contract="HO", month=13, year=2020)
        fail(f"Should have raised ValueError, but got {symbol}")
    except ValueError as e:
        assert_that(str(e)).is_equal_to("month must be btw 1-12, or a CME Month Code")


def futures_symbol_w_bad_year_test():
    try:
        symbol = Symbol(contract="HO", month=12, year=2000)
        fail(f"Should have raised ValueError, but got {symbol}")
    except ValueError as e:
        assert_that(str(e)).starts_with("year must be between")


def futures_symbol_w_cme_month_lowercase_test():
    symbol = Symbol(contract="HO", month="f", year=2020)

    assert_that(str(symbol)).is_equal_to("HOF20")


def options_symbol_init_test():
    symbol = Symbol(contract="HO", month=1, year=2020, put_call="C", strike=1.23)

    assert_that(str(symbol)).is_equal_to("HOF20C1.23")
