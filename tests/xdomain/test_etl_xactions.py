from assertpy import assert_that  # type: ignore
import pandas as pd
import pytest

from src.xdomain.etl.xactions import (
    extract_rjo_raw_data,
    read_rjo_data_file,
    RJO_HEADERS,
    TRADE_DATA_FIELDS,
)


@pytest.fixture
def raw_rjo_dataframe():
    return pd.read_csv("./data/valid_file.csv", names=RJO_HEADERS, engine="python")


# Returns a pandas DataFrame object when given a valid filename.
def returns_dataframe_with_valid_filename_test():
    filename = "./data/valid_file.csv"
    result = read_rjo_data_file(filename, headers=RJO_HEADERS)
    assert_that(isinstance(result, pd.DataFrame)).is_true()


# Returns an empty DataFrame object when given a non-existent filename.
def returns_empty_dataframe_with_nonexistent_filename_test():
    filename = "./data/nonexistent_file.csv"
    result = read_rjo_data_file(filename, headers=RJO_HEADERS)
    assert_that(isinstance(result, pd.DataFrame)).is_true()
    assert_that(result.empty).is_true()
    assert_that(result.columns.tolist()).contains_only(*RJO_HEADERS)


def returns_dataframe_with_correct_rows_test():
    filename = "./data/valid_file.csv"
    result = read_rjo_data_file(filename, headers=RJO_HEADERS)
    assert_that(isinstance(result, pd.DataFrame)).is_true()
    assert_that(result.columns.tolist()).contains_only(*RJO_HEADERS)
    assert_that(len(result)).is_greater_than(0)


def returns_extracted_rjo_data_from_dataframe_test(raw_rjo_dataframe):
    result = extract_rjo_raw_data(raw_rjo_dataframe)
    assert_that(isinstance(result, pd.DataFrame)).is_true()
    assert_that(result.columns.tolist()).contains_only(*TRADE_DATA_FIELDS)
    assert_that(len(result)).is_greater_than(0)
