from assertpy import assert_that  # type: ignore
import pandas as pd
import pytest

from src.constants import NYMEX, ICE, RJO_HEADERS
from src.xdomain.etl.rjo.actions import (
    clearing_cost_for_date_acct_mkt,
    commission_from_row,
    contract_type_from_row,
    net_commission_from_row,
    trade_market_from_row,
)

COQUEST_ICE_RATE = 1.85
COQUEST_BASE_RATE = 2.00
RJO_BASE_RATE = 0.85


@pytest.fixture
def raw_rjo_dataframe():
    return pd.read_csv("./data/valid_file.csv", names=RJO_HEADERS, engine="python")


@pytest.fixture
def igs_4_acct():
    return 84478


@pytest.fixture
def igs_5_acct():
    return 85478


@pytest.fixture
def wrangler_acct():
    return 84663


def correct_mkt_from_row_w_NYM_test(raw_rjo_dataframe):
    mkt = trade_market_from_row(raw_rjo_dataframe.iloc[2])

    assert_that(mkt).is_equal_to(NYMEX)


def correct_mkt_from_row_w_ICE_test(raw_rjo_dataframe):
    mkt = trade_market_from_row(raw_rjo_dataframe.iloc[15])

    assert_that(mkt).is_equal_to(ICE)


def default_mkt_from_row_w_neither_ICE_or_NYM_test(raw_rjo_dataframe):
    nat_gas = trade_market_from_row(raw_rjo_dataframe.iloc[33])

    assert_that(nat_gas).is_equal_to(NYMEX)


def cash_mkt_from_row_w_wires_and_ach_test(raw_rjo_dataframe):
    wire_xfer = trade_market_from_row(raw_rjo_dataframe.iloc[65])
    ach_push = trade_market_from_row(raw_rjo_dataframe.iloc[0])

    assert_that(wire_xfer).is_equal_to(NYMEX)
    assert_that(ach_push).is_equal_to(NYMEX)


def futures_type_for_futures_row_test(raw_rjo_dataframe):
    nat_gas = contract_type_from_row(raw_rjo_dataframe.iloc[33])
    ice_futures = contract_type_from_row(raw_rjo_dataframe.iloc[15])
    nym_futures = contract_type_from_row(raw_rjo_dataframe.iloc[2])

    assert_that(nat_gas).is_equal_to("F")
    assert_that(ice_futures).is_equal_to("F")
    assert_that(nym_futures).is_equal_to("F")


def na_type_for_cash_transaction_rows_test(raw_rjo_dataframe):
    wire_xfer = contract_type_from_row(raw_rjo_dataframe.iloc[65])
    ach_push = contract_type_from_row(raw_rjo_dataframe.iloc[0])

    assert_that(wire_xfer).is_equal_to("N")
    assert_that(ach_push).is_equal_to("N")


def call_type_for_call_option_row_test(raw_rjo_dataframe):
    ice_pjm_call = contract_type_from_row(raw_rjo_dataframe.iloc[175])

    assert_that(ice_pjm_call).is_equal_to("C")


def clearing_rate_for_2016_test():
    rate = clearing_cost_for_date_acct_mkt(20160101, 123456, NYMEX)

    assert_that(rate).is_equal_to(2.00)


def clearing_rate_for_2010_test():
    rate = clearing_cost_for_date_acct_mkt(20100101, 123456, NYMEX)

    assert_that(rate).is_equal_to(0.00)


def clearing_rate_for_2021_june_test(igs_4_acct, igs_5_acct):
    acct_1_nymex = clearing_cost_for_date_acct_mkt(20210601, 123456, NYMEX)
    acct_1_ice = clearing_cost_for_date_acct_mkt(20210601, 123456, ICE)
    acct_4_nymex = clearing_cost_for_date_acct_mkt(20210601, igs_4_acct, NYMEX)
    acct_4_ice = clearing_cost_for_date_acct_mkt(20210601, igs_4_acct, ICE)
    acct_5_nymex = clearing_cost_for_date_acct_mkt(20210601, igs_5_acct, NYMEX)
    acct_5_ice = clearing_cost_for_date_acct_mkt(20210601, igs_5_acct, ICE)

    assert_that(acct_1_ice).is_equal_to(COQUEST_BASE_RATE)
    assert_that(acct_1_nymex).is_equal_to(COQUEST_BASE_RATE)
    assert_that(acct_4_nymex).is_equal_to(COQUEST_BASE_RATE)
    assert_that(acct_4_ice).is_equal_to(COQUEST_BASE_RATE)
    assert_that(acct_5_nymex).is_equal_to(COQUEST_BASE_RATE)
    assert_that(acct_5_ice).is_equal_to(COQUEST_BASE_RATE)


def clearing_rate_for_2021_july_test(igs_4_acct, igs_5_acct, wrangler_acct):
    acct_1_nymex = clearing_cost_for_date_acct_mkt(20210701, 123456, NYMEX)
    acct_1_ice = clearing_cost_for_date_acct_mkt(20210701, 123456, ICE)
    acct_4_nymex = clearing_cost_for_date_acct_mkt(20210701, igs_4_acct, NYMEX)
    acct_4_ice = clearing_cost_for_date_acct_mkt(20210701, igs_4_acct, ICE)
    acct_5_nymex = clearing_cost_for_date_acct_mkt(20210701, igs_5_acct, NYMEX)
    acct_5_ice = clearing_cost_for_date_acct_mkt(20210701, igs_5_acct, ICE)
    wrangler_nymex = clearing_cost_for_date_acct_mkt(20210701, wrangler_acct, NYMEX)
    wrangler_ice = clearing_cost_for_date_acct_mkt(20210701, wrangler_acct, ICE)

    assert_that(acct_1_ice).is_equal_to(COQUEST_BASE_RATE)
    assert_that(acct_1_nymex).is_equal_to(COQUEST_BASE_RATE)
    assert_that(acct_4_nymex).is_equal_to(COQUEST_BASE_RATE)
    assert_that(acct_4_ice).is_equal_to(COQUEST_BASE_RATE)
    assert_that(acct_5_nymex).is_equal_to(COQUEST_BASE_RATE)
    assert_that(acct_5_ice).is_equal_to(COQUEST_BASE_RATE)
    assert_that(wrangler_nymex).is_equal_to(COQUEST_ICE_RATE)
    assert_that(wrangler_ice).is_equal_to(COQUEST_ICE_RATE)


def clearing_rate_for_2021_dec_test(igs_4_acct, igs_5_acct, wrangler_acct):
    acct_1_nymex = clearing_cost_for_date_acct_mkt(20211201, 123456, NYMEX)
    acct_1_ice = clearing_cost_for_date_acct_mkt(20211201, 123456, ICE)
    acct_4_nymex = clearing_cost_for_date_acct_mkt(20211201, igs_4_acct, NYMEX)
    acct_4_ice = clearing_cost_for_date_acct_mkt(20211201, igs_4_acct, ICE)
    acct_5_nymex = clearing_cost_for_date_acct_mkt(20211201, igs_5_acct, NYMEX)
    acct_5_ice = clearing_cost_for_date_acct_mkt(20211201, igs_5_acct, ICE)
    wrangler_nymex = clearing_cost_for_date_acct_mkt(20211201, wrangler_acct, NYMEX)
    wrangler_ice = clearing_cost_for_date_acct_mkt(20211201, wrangler_acct, ICE)

    assert_that(acct_1_ice).is_equal_to(COQUEST_BASE_RATE)
    assert_that(acct_1_nymex).is_equal_to(COQUEST_BASE_RATE)
    assert_that(acct_4_nymex).is_equal_to(COQUEST_BASE_RATE)
    assert_that(acct_4_ice).is_equal_to(COQUEST_ICE_RATE)
    assert_that(acct_5_nymex).is_equal_to(COQUEST_BASE_RATE)
    assert_that(acct_5_ice).is_equal_to(COQUEST_ICE_RATE)
    assert_that(wrangler_nymex).is_equal_to(COQUEST_ICE_RATE)
    assert_that(wrangler_ice).is_equal_to(COQUEST_ICE_RATE)


def clearing_rate_for_2022_may_5_test(igs_4_acct, igs_5_acct, wrangler_acct):
    acct_1_nymex = clearing_cost_for_date_acct_mkt(20220505, 123456, NYMEX)
    acct_1_ice = clearing_cost_for_date_acct_mkt(20220505, 123456, ICE)
    acct_4_nymex = clearing_cost_for_date_acct_mkt(20220505, igs_4_acct, NYMEX)
    acct_4_ice = clearing_cost_for_date_acct_mkt(20220505, igs_4_acct, ICE)
    acct_5_nymex = clearing_cost_for_date_acct_mkt(20220505, igs_5_acct, NYMEX)
    acct_5_ice = clearing_cost_for_date_acct_mkt(20220505, igs_5_acct, ICE)
    wrangler_nymex = clearing_cost_for_date_acct_mkt(20220505, wrangler_acct, NYMEX)
    wrangler_ice = clearing_cost_for_date_acct_mkt(20220505, wrangler_acct, ICE)

    assert_that(acct_1_ice).is_equal_to(COQUEST_BASE_RATE)
    assert_that(acct_1_nymex).is_equal_to(COQUEST_BASE_RATE)
    assert_that(acct_4_nymex).is_equal_to(COQUEST_BASE_RATE)
    assert_that(acct_4_ice).is_equal_to(COQUEST_ICE_RATE)
    assert_that(acct_5_nymex).is_equal_to(COQUEST_BASE_RATE)
    assert_that(acct_5_ice).is_equal_to(COQUEST_ICE_RATE)
    assert_that(wrangler_nymex).is_equal_to(COQUEST_ICE_RATE)
    assert_that(wrangler_ice).is_equal_to(COQUEST_ICE_RATE)


def clearing_rate_for_2024_jan_test(igs_4_acct, igs_5_acct, wrangler_acct):
    acct_1_nymex = clearing_cost_for_date_acct_mkt(20240114, 123456, NYMEX)
    acct_1_ice = clearing_cost_for_date_acct_mkt(20240114, 123456, ICE)
    acct_4_nymex = clearing_cost_for_date_acct_mkt(20240114, igs_4_acct, NYMEX)
    acct_4_ice = clearing_cost_for_date_acct_mkt(20240114, igs_4_acct, ICE)
    acct_5_nymex = clearing_cost_for_date_acct_mkt(20240114, igs_5_acct, NYMEX)
    acct_5_ice = clearing_cost_for_date_acct_mkt(20240114, igs_5_acct, ICE)
    wrangler_nymex = clearing_cost_for_date_acct_mkt(20240114, wrangler_acct, NYMEX)
    wrangler_ice = clearing_cost_for_date_acct_mkt(20240114, wrangler_acct, ICE)

    assert_that(acct_1_ice).is_equal_to(RJO_BASE_RATE)
    assert_that(acct_1_nymex).is_equal_to(RJO_BASE_RATE)
    assert_that(acct_4_nymex).is_equal_to(RJO_BASE_RATE)
    assert_that(acct_4_ice).is_equal_to(RJO_BASE_RATE)
    assert_that(acct_5_nymex).is_equal_to(RJO_BASE_RATE)
    assert_that(acct_5_ice).is_equal_to(RJO_BASE_RATE)
    assert_that(wrangler_nymex).is_equal_to(RJO_BASE_RATE)
    assert_that(wrangler_ice).is_equal_to(RJO_BASE_RATE)


def commission_from_row_test(raw_rjo_dataframe):
    trade_data = raw_rjo_dataframe.iloc[2]
    comm = commission_from_row(trade_data)

    assert_that(comm).is_equal_to(trade_data.Commission * -1)


def net_commission_from_row_test(raw_rjo_dataframe):
    trade_data = raw_rjo_dataframe.iloc[2]
    net = net_commission_from_row(trade_data)

    assert_that(net).is_equal_to(
        (trade_data.Commission * -1) - (trade_data.Quantity * RJO_BASE_RATE)
    )
