from assertpy import assert_that  # type: ignore
import pandas as pd
import pytest

from string import Template
from src.xdomain.etl.rjo.constants import TRADE_DATA_FIELDS, RJO_HEADERS
from src.xdomain.etl.rjo import repo


@pytest.fixture
def empty_filename():
    return "./data/empty_file.csv"


@pytest.fixture
def test_directory_template():
    return Template("./tests/data/$year")


@pytest.fixture
def test_file_template():
    return Template("POWERHOUSE_csvth1_dth1_$year$month$day.csv")


def returns_empty_df_from_empty_file_test(empty_filename):
    result = repo.read_csv_data(empty_filename)

    assert_that(isinstance(result, pd.DataFrame)).is_true()
    assert_that(result.empty).is_true()
    assert_that(result.columns.tolist()).is_empty()


def returns_empty_df_w_cols_from_empty_file_test(empty_filename):
    result = repo.read_csv_data(empty_filename, headers=TRADE_DATA_FIELDS)

    assert_that(isinstance(result, pd.DataFrame)).is_true()
    assert_that(result.empty).is_true()
    assert_that(result.columns.tolist()).contains_only(*TRADE_DATA_FIELDS)


def computes_correct_filename_given_ymd_inputs_test(
    test_directory_template, test_file_template
):
    result = repo.get_daily_filename_for_year_month_day(
        2024, 1, 31, test_directory_template, test_file_template
    )
    expected_dir = test_directory_template.substitute(year=2024)
    expected_file = test_file_template.substitute(year=2024, month="01", day="31")

    assert_that(isinstance(result, str)).is_true()
    assert_that(result).is_equal_to(f"{expected_dir}/{expected_file}")


def raises_value_errors_when_computing_correct_filename_w_missing_inputs_test(
    test_directory_template, test_file_template
):
    assert_that(repo.get_daily_filename_for_year_month_day).raises(
        ValueError
    ).when_called_with(2024, 0, 0)
    assert_that(repo.get_daily_filename_for_year_month_day).raises(
        ValueError
    ).when_called_with(2004, 1, 31)


def read_csv_data_returns_daily_data_properly_test(
    test_file_template, test_directory_template
):
    filename = test_file_template.substitute(year=2024, month="01", day="31")
    filename = f"{test_directory_template.substitute(year=2024)}/{filename}"
    result = repo.read_csv_data(filename, headers=RJO_HEADERS)

    assert_that(isinstance(result, pd.DataFrame)).is_true()
    assert_that(result.empty).is_false()
    assert_that(result.columns.tolist()).contains_only(*RJO_HEADERS)
