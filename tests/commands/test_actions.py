import arrow
from assertpy import assert_that  # type: ignore

from src.commands.actions import get_fee_schedule
from src.xdomain.constants import FEE_SCHEDULE_2023, FEE_SCHEDULE_AS_RJO_GIB


def get_2024_fee_schedule_test():
    trade_date = arrow.get("2024-01-01")
    fee_schedule = get_fee_schedule(trade_date)

    assert_that(fee_schedule).is_equal_to(FEE_SCHEDULE_AS_RJO_GIB)


def get_2020_fee_schedule_test():
    trade_date = arrow.get("2020-01-05")
    fee_schedule = get_fee_schedule(trade_date)

    assert_that(fee_schedule).is_equal_to(FEE_SCHEDULE_2023)
