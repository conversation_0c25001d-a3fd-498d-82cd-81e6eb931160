from dataclasses import dataclass
from string import Template
import re

import arrow
import click
from loguru import logger as log
import pandas as pd

from src.xdomain.etl.rjo.repo import RJORepo as Repo
from src.infrastructure.display import display_df, display_stats, echo_stat 


SUMMARY_FIELDS = [
    "Record_Type",
    "Trade_Date",
    "Account_Number",
    "Account_Name",
    "Contract",
    "Contract_Type",
    "Market",
]


@dataclass
class IntDate:
    date_as_int: int

    def __post_init__(self):
        if isinstance(self.date_as_int, IntDate):
            self.date_as_int = self.date_as_int.date_as_int
        if not isinstance(self.date_as_int, int):
            raise ValueError(
                f"date_as_int must be an int, not {type(self.date_as_int)}"
            )

    @property
    def month(self):
        return (self.date_as_int // 100) - ((self.date_as_int // 10000) * 100)

    @property
    def day(self):
        return self.date_as_int % 100

    @property
    def year(self):
        return self.date_as_int // 10000

    @property
    def first_of_month(self):
        year = self.year
        return IntDate(int(f"{year}{self.month:02}01"))

    @property
    def month_end(self):
        if self.month == 2:
            if self.year % 4 == 0:
                return IntDate(int(f"{self.year}{self.month:02}29"))
            return IntDate(int(f"{self.year}{self.month:02}28"))
        if self.month in [1, 3, 5, 7, 8, 10, 12]:
            return IntDate(int(f"{self.year}{self.month:02}31"))
        return IntDate(int(f"{self.year}{self.month:02}30"))

    def format(self, format_str: str) -> str:
        log.info(f"date: {self.date_as_int}, format_str: {format_str}")
        return arrow.get(str(self.date_as_int)).format(format_str)

    def __eq__(self, other):
        if isinstance(other, IntDate):
            return self.date_as_int == other.date_as_int
        if isinstance(other, int):
            return self.date_as_int == other
        if isinstance(other, str) and other.isnumeric():
            return self.date_as_int == int(other)
        if isinstance(other, arrow.Arrow):
            return self.date_as_int == int(other.format("YYYYMMDD"))
        return NotImplemented

    def __lt__(self, other):
        if isinstance(other, IntDate):
            return self.date_as_int < other.date_as_int
        if isinstance(other, int):
            return self.date_as_int < other
        if isinstance(other, str) and other.isnumeric():
            return self.date_as_int < int(other)
        if isinstance(other, arrow.Arrow):
            return self.date_as_int < int(other.format("YYYYMMDD"))
        return NotImplemented

    def __gt__(self, other):
        if isinstance(other, IntDate):
            return self.date_as_int > other.date_as_int
        if isinstance(other, int):
            return self.date_as_int > other
        if isinstance(other, str) and other.isnumeric():
            return self.date_as_int > int(other)
        if isinstance(other, arrow.Arrow):
            return self.date_as_int > int(other.format("YYYYMMDD"))
        return NotImplemented

    def __le__(self, other):
        if isinstance(other, IntDate):
            return self.date_as_int <= other.date_as_int
        if isinstance(other, int):
            return self.date_as_int <= other
        if isinstance(other, str) and other.isnumeric():
            return self.date_as_int <= int(other)
        if isinstance(other, arrow.Arrow):
            return self.date_as_int <= int(other.format("YYYYMMDD"))
        return NotImplemented

    def __ge__(self, other):
        if isinstance(other, IntDate):
            return self.date_as_int >= other.date_as_int
        if isinstance(other, int):
            return self.date_as_int >= other
        if isinstance(other, str) and other.isnumeric():
            return self.date_as_int >= int(other)
        if isinstance(other, arrow.Arrow):
            return self.date_as_int >= int(other.format("YYYYMMDD"))
        return NotImplemented


@click.group()
def stats():
    """group command base function"""
    pass


@click.command()
@click.option("--year", default=arrow.utcnow().year, help="The year to update")
def update(year: int):
    year = year or arrow.now().year
    do_update(year)


def do_update(year: int) -> None:
    """Update the stats for a given year"""
    stats_repo = Repo(agg_dir_template=Template("./data/temp/$year"))
    stats = stats_repo.update_stats(year)
    stats_repo.save_stats(stats, year)


def display_single_day_stats_comparison(cy, ly, cy_date, ly_date) -> None:
    """Display the comparison of stats for a single day"""
    day = cy[cy.Entry_Date == cy_date]
    last_year = ly[ly.Entry_Date == ly_date]
    display_stats(day, f"Today {arrow.get(str(cy_date)).format('MMM D, YYYY')}")
    display_stats(
        last_year, f"Last Year {arrow.get(str(ly_date)).format('MMM D, YYYY')}"
    )


def display_ytd_stats_comparison(cy, ly, cy_date, ly_date) -> None:
    """Display the comparison of stats for year to date"""
    ytd = cy[cy.Entry_Date <= cy_date]
    ly_ytd = ly[ly.Entry_Date <= ly_date]
    display_stats(ytd, f"Curr Year To Date {cy_date.format('MMM D, YYYY')}")
    display_stats(ly_ytd, f"Last Year To Date {ly_date.format('MMM D, YYYY')}")


def display_mtd_stats_comparison(cy, ly, cy_date, ly_date) -> None:
    """Display the comparison of stats for year to date"""
    mtd = cy[(cy.Entry_Date <= cy_date) & (cy.Entry_Date >= cy_date.first_of_month)]
    ly_mtd = ly[(ly.Entry_Date <= ly_date) & (ly.Entry_Date >= ly_date.first_of_month)]
    display_stats(mtd, f"Curr Year Month-To-Date {cy_date.format('MMM D, YYYY')}")
    display_stats(ly_mtd, f"Last Year Month-To-Date {ly_date.format('MMM D, YYYY')}")


def display_trades_by_cust(trade_data, sort_col, title):
    agg_data = (
        trade_data.groupby(SUMMARY_FIELDS)
        .agg({"Commission": "sum", "Quantity": "sum", "Net_Commission": "sum"})
        .reset_index()
    )
    agg_data.columns = agg_data.columns.get_level_values(0)
    display_df(agg_data, sort_col, title)


def display_cust_year_over_year(cy, ly, cy_date, ly_date):
    """Display the comparison of stats for year to date"""
    curr_year = cy[cy.Entry_Date <= cy_date]
    curr_ytd_by_cust = (
        curr_year.groupby(["Account_Name"])[
            ["Commission", "Quantity", "Net_Commission"]
        ]
        .sum()
        .reset_index()
    )
    last_year = ly[ly.Entry_Date <= ly_date]
    prev_ytd_by_cust = (
        last_year.groupby(["Account_Name"])[
            ["Commission", "Quantity", "Net_Commission"]
        ]
        .sum()
        .reset_index()
        .rename(
            columns={
                "Commission": "Prev_Commission",
                "Net_Commission": "Prev_Net_Commission",
                "Quantity": "Prev_Quantity",
            }
        )
    )  # noqa: E501
    by_cust = pd.merge(
        prev_ytd_by_cust,
        curr_ytd_by_cust,
        on="Account_Name",
        how="outer",
        validate="m:m",
    ).fillna(
        0.0
    )  # noqa: E501
    display_df(
        by_cust[
            [
                "Account_Name",
                "Prev_Commission",
                "Prev_Quantity",
                "Prev_Net_Commission",
                "Commission",
                "Quantity",
                "Net_Commission",
            ]
        ],
        "-Net_Commission",
        "Year Over Year by Customer",
        show_index=True,
    )


@click.command()
@click.option(
    "--year", default=arrow.utcnow().shift(days=-1).year, help="The year to update"
)
@click.option(
    "--month", default=arrow.utcnow().shift(days=-1).month, help="The month to update"
)
@click.option(
    "--day", default=arrow.utcnow().shift(days=-1).day, help="The day to update"
)
def daily(year: int, month: int, day: int):
    year = year or arrow.now().year
    month = month or arrow.now().month
    day = day or arrow.now().day
    today = int(f"{year}{month:02}{day:02}")
    ly_day = day - 1 if day == 29 and month == 2 else day
    last_year = int(f"{year-1}{month:02}{ly_day:02}")
    log.info(f"year type: {type(year)}, year value: {year}")
    do_update(year)
    stats_repo = Repo(agg_dir_template=Template("./data/temp/$year"))
    cy_stats = stats_repo.get_trade_stats(year, month, day)
    ly_stats = stats_repo.get_trade_stats(year - 1, month, day)
    display_ytd_stats_comparison(cy_stats, ly_stats, IntDate(today), IntDate(last_year))
    display_mtd_stats_comparison(cy_stats, ly_stats, IntDate(today), IntDate(last_year))
    display_single_day_stats_comparison(cy_stats, ly_stats, today, last_year)
    display_trades_by_cust(
        cy_stats[cy_stats.Entry_Date == today], "-Commission", "Trades by Customer"
    )
    display_cust_year_over_year(cy_stats, ly_stats, IntDate(today), IntDate(last_year))


@click.command()
@click.option(
    "--year", default=arrow.utcnow().shift(days=-1).year, help="The year to update"
)
@click.option(
    "--month", default=arrow.utcnow().shift(months=-1).month, help="The month to update"
)
def month_end(year: int, month: int):
    year = year or arrow.now().year
    month = month or arrow.now().month
    cy_month = IntDate(int(f"{year}{month:02}01"))
    month_end = IntDate(int(f"{year}{month:02}01")).month_end
    ly_month_end = IntDate(int(f"{year-1}{month:02}01")).month_end
    stats_repo = Repo(agg_dir_template=Template("./data/temp/$year"))
    cy_stats = stats_repo.get_trade_stats(
        month_end.year, month_end.month, month_end.day
    )
    ly_stats = stats_repo.get_trade_stats(
        ly_month_end.year, ly_month_end.month, ly_month_end.day
    )
    display_mtd_stats_comparison(
        cy_stats,
        ly_stats,
        IntDate(month_end),
        IntDate(ly_month_end),
    )
    display_trades_by_cust(
        cy_stats[
            (cy_stats.Entry_Date >= cy_month)
            & (cy_stats.Entry_Date <= cy_month.month_end)
        ],
        "Account_Number",
        "Trades by Customer",
    )


@click.command()
@click.option(
    "--year", default=arrow.utcnow().shift(days=-1).year, help="The year to update"
)
@click.option(
    "--month", default=arrow.utcnow().shift(days=-1).month, help="The month to update"
)
@click.option(
    "--day", default=arrow.utcnow().shift(days=-1).day, help="The day to update"
)
def positions(year: int, month: int, day: int):
    echo_stat("   Positions", 0)
    filename = f"./data/csvnpos_npos_{year}{month:02}{day:02}.csv"
    raw = pd.read_csv(filename)
    raw['Contract Name'] = raw.apply(contract_desc_by_row, axis=1)
    raw['Contract Type'] = raw.apply(contract_type_by_row, axis=1)
    raw['Contract Month'] = raw.apply(contract_exp_by_row, axis=1)
    positions = raw[['Account Number', 'Quantity', 'Contract Month', 'Contract Name', 'Contract Code']]
    display_df(positions.head(40).sort_values(by=["Contract Month", "Account Number", "Contract Code"]), show_index=False, title="Positions")
    by_account = positions.groupby(['Account Number', 'Contract Month', 'Contract Name', 'Contract Code']).sum().reset_index()
    display_df(by_account.sort_values(by=["Contract Month", "Account Number", "Contract Code"]), show_index=False, title="Positions Overhang by Account")
    month_positions = positions[['Quantity', 'Contract Month']].groupby(['Contract Month']).sum().reset_index()
    display_df(month_positions.sort_values(by=["Contract Month"]), show_index=False, title="Positions Overhang by Month")
    # accounts = raw[['Account Number', 'Account Name']].drop_duplicates()
    # # display_df(accounts.sort_values(by="Account Name"), show_index=False, title="Accounts")
    # accounts.to_csv("./data/accounts.csv", mode="w", index=False)
    # contracts = raw[['Contract Code', 'Security Desc Line 1']].drop_duplicates()
    # display_df(contracts.sort_values(by="Contract Code"), show_index=False, title="Contracts")
    # positions = raw[['Account Number', 'Quantity', 'Contract Month', 'Security Desc Line 1', 'Security Type Code', 'Security Subtype Code', 'Contract Code']]
    # positions['Contract Type'] = positions.apply(contract_type_by_row, axis=1)
    # positions['Contract Name'] = positions.apply(contract_desc_by_row, axis=1)
    # positions = positions[['Account Number', 'Quantity', 'Contract Month', 'Contract Type', 'Contract Code', 'Contract Name']]
    # display_df(positions[['Contract Name', 'Contract Code', 'Contract Month']].drop_duplicates().sort_values(by=["Contract Month", "Contract Code"]), show_index=False, title="Contracts Named")
    # # display_df(positions.head(20), show_index=False)
    # summ = positions.groupby(['Account Number', 'Contract Month', 'Contract Type', 'Contract Code']).sum().reset_index()
    # summ = summ[['Contract Month', 'Quantity']]
    # summ = summ.groupby(['Contract Month']).sum().reset_index()
    # # display_df(summ.sort_values(by=["Contract Month"]).head(30), show_index=False)


def contract_type_by_row(row) -> str:
    """will be used to determine the type of contract"""
    return row['Security Subtype Code'] if row['Security Subtype Code'] in ["C", "P"] else "F"

def contract_desc_by_row(row) -> str:
    """will be used to determine the type of contract"""
    pattern = r'\b(?:JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC)\b|\b\d{2}\b'
    option_pattern = r'[P|C]?\d{4,5}'
    # Use re.sub to replace the matched patterns with an empty string
    result = re.sub(pattern, '', row['Security Desc Line 1'])
    result = re.sub(option_pattern, '', result)
    # Remove extra spaces that might result from the replacements
    result = re.sub(r'\s+', ' ', result).strip()
    
    return result


def contract_exp_by_row(row, curr_year=2024) -> str:
    """will be used to determine the type of contract"""
    row_year = row['Contract Month'] // 100
    return row_year if row_year > curr_year else row['Contract Month'] / 100


stats.add_command(update)
stats.add_command(daily)
stats.add_command(month_end)
stats.add_command(positions)

if __name__ == "__main__":
    log.info("Starting...")
    stats()
    log.info("Done.")
