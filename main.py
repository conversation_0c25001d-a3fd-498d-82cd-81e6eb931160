import re
from os import listdir
from os.path import join
from string import Template

import arrow
import click
import pandas as pd
from loguru import logger as log

RJO_AGG_STATS_FILE_HEADERS = [
    "Record_Type",
    "Account_Number",
    "Account_Name",
    "Contract",
    "Trade_Month",
    "Trade_Day",
    "Commission",
    "Quantity",
]
STAT_FIELDS = [
    "Record_Type",
    "Account_Number",
    "Account_Name",
    "Contract",
    "Trade_Date",
    "Security_Desc_1",
    "Quantity",
    "Commission",
    "Trade_Month",
    "Trade_Day",
]

RJO_DATA_DIR = Template("./data/$year")
RJO_FILENAME_TEMPL = Template("POWERHOUSE_csvth1_dth1_$year$month$day.csv")
RJO_HEADERS = [
    "Record_Type",
    "Firm_Code",
    "Office",
    "Salesman",
    "Account_Number",
    "Account_Class",
    "Account_Subclass",
    "Account_Type",
    "Card_Number",
    "Buy_Sell",
    "Quantity",
    "Security_Desc_1",
    "Formatted_Trade_Price",
    "Trade_Date",
    "Open_Close",
    "Contract_Month",
    "Contract_Day",
    "Security_Type",
    "Security_Subtype",
    "Option_Strike_Price",
    "Option_Expire_Date",
    "Subaccount_ID",
    "Broker",
    "Spread",
    "Account_Name",
    "Source_Account_Number",
    "Settlement_Date",
    "Exchange",
    "Trading_Exchange",
    "Contract",
    "Contract_Symbol",
    "Product_Type",
    "Trade_Price",
    "Trade_Calc_Control",
    "Comment_1",
    "Comment_2",
    "Executing_Broker",
    "Give_Up",
    "Give_Up_Firm",
    "Buy_Quantity",
    "Sell_Quantity",
    "Account_Type_Currency",
    "Commission",
    "Clearing_Fee",
    "Exchange_Fee",
    "NFA_Fee",
    "Global_Desk_Charge",
    "RJO_Trans_Fee",
    "IB_Trans_Fee",
    "Charge_Amount_Give_Up",
    "Charge_Amount_Brokerage",
    "Charge_Amount_Other",
    "Total_Net_Charge",
    "Multiplication_Factor",
    "Market_Value",
    "Close_Price",
    "Underlying_Close_Price",
    "CUSIP_Code",
    "Commission_Account_Type",
    "Charge_Clearing_Account_Type",
    "Charge_Exchange_Account_Type",
    "Charge_NFA_Account_Type",
    "Charge_Global_Desk_Account_Type",
    "Charge_RJO_Trans_Account_Type",
    "Charge_IB_Trans_Account_Type",
    "Charge_Give_Up_Account_Type",
    "Charge_Brokerage_Account_Type",
    "Charge_Other_Account_Type",
    "Tracer_Number",
    "Order_Number",
    "Bloomberg_Root",
    "Bloomberg_Exchange",
    "Bloomberg_Market_Sector",
    "Option_Premium",
    "Security_Desc_2",
    "Security_Desc_3",
    "Trade_Desc_1",
    "Trade_Desc_2",
    "Trade_Desc_3",
    "Delta_Factor",
    "Product_Currency",
    "Commission_Field_Define_Code",
    "Charge_Clearing_Field_Define_Code",
    "Charge_Exchange_Field_Define_Code",
    "Charge_NFA_Field_Define_Code",
    "Charge_Global_Desk_Field_Define_Code",
    "Charge_RJO_Field_Define_Code",
    "Charge_IB_Trans_Field_Define_Code",
    "Charge_Give_Up_Field_Define_Code",
    "Charge_Brokerage_Field_Define_Code",
    "Charge_Other_Field_Define_Code",
    "Charge_Clearing_Field_Usage_Code",
    "Charge_Exchange_Field_Usage_Code",
    "Charge_NFA_Field_Usage_Code",
    "Charge_Global_Desk_Field_Usage_Code",
    "Charge_RJO_Field_Usage_Code",
    "Charge_IB_Trans_Field_Usage_Code",
    "Charge_Give_Up_Field_Usage_Code",
    "Charge_Brokerage_Field_Usage_Code",
    "Charge_Other_Field_Usage_Code",
    "Entry_Date",
    "Round_Half_Turn",
]
MONTHS = [
    "None",
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
]

# Markets
NYMEX = "NYM"
ICE = "ICE"

# Fee Schedule Constants
DEFAULT_TRADE_FEE = 2
ICE_TRADE_FEE = 1.85
FEE_SCHEDULE = {
    "default": {
        ICE: DEFAULT_TRADE_FEE,
        NYMEX: DEFAULT_TRADE_FEE,
    },
    84663: {  # WRANGLER
        ICE: ICE_TRADE_FEE,
        NYMEX: ICE_TRADE_FEE,
    },
    84478: {  # IGS
        ICE: ICE_TRADE_FEE,
        NYMEX: DEFAULT_TRADE_FEE,
    },
}


def read_rjo_agg_file(filename):
    raw = pd.DataFrame()
    try:
        raw = pd.read_csv(filename)
        raw.dropna(subset=["Account_Number"], inplace=True)
        raw["Account_Number"] = raw["Account_Number"].astype(int)
        raw["Trade_Month"] = raw["Trade_Month"].astype(int)
        raw["Trade_Day"] = raw["Trade_Day"].astype(int)
    except FileNotFoundError:
        log.warning(f"File not found: {filename}")
    return raw


def read_rjo_daily_data(filename):
    df = pd.read_csv(filename, names=RJO_HEADERS)
    df.dropna(subset=["Account_Number"], inplace=True)
    df["Account_Number"] = df["Account_Number"].astype(int)
    return df


def trade_month_from_row(row):
    return int(row.Entry_Date / 100)


def trade_day_from_row(row):
    return int(row.Entry_Date) - int(row.Entry_Date / 100) * 100


def trade_market_from_row(row):
    mkt = NYMEX
    z = re.match(r".*[A-Z]{3}\s\d{2}\s([A-Z]{3})\s", row.Security_Desc_1)
    if z:
        mkt = z.groups(1)[0]
    return mkt if mkt in [NYMEX, ICE] else NYMEX


def net_commission_from_row(record):
    account_fee_structure = FEE_SCHEDULE.get(
        record.Account_Number, FEE_SCHEDULE["default"]
    )
    per_trade_fee = account_fee_structure.get(record["Market"], DEFAULT_TRADE_FEE)
    return (
        record["Commission"] + (record["Quantity"] * per_trade_fee)
        if record["Commission"]
        else 0
    )


def last_agg_date_ymd(df):
    if df.empty:
        return arrow.utcnow().year, 1, 1
    year_month = df.Trade_Month.max()
    day = df[df.Trade_Month == year_month].Trade_Day.max()
    log.debug(f"day = {day}")
    year = int(year_month / 100)
    log.debug(f"year = {year}")
    month = int(year_month - year * 100)
    log.debug(f"month = {month}")
    day = int(day)
    return year, month, day


def clean_rjo_data(raw):
    raw["Account_Number"] = raw["Account_Number"].astype(int)
    raw["Trade_Month"] = raw.apply(trade_month_from_row, axis=1)
    raw["Trade_Day"] = raw.apply(trade_day_from_row, axis=1)
    raw["Market"] = raw.apply(trade_market_from_row, axis=1)
    raw["Net_Commission"] = raw.apply(net_commission_from_row, axis=1)
    log.debug(f"raw: {raw}")
    return raw


def summarize_rjo_daily_data(raw):
    grouped = raw.groupby(
        [
            "Record_Type",
            "Account_Number",
            "Account_Name",
            "Contract",
            "Trade_Date",
            "Trade_Month",
            "Trade_Day",
            "Market",
        ]
    ).agg({"Commission": ["sum"], "Quantity": ["sum"], "Net_Commission": ["sum"]})
    grouped = grouped.reset_index()
    grouped.columns = grouped.columns.get_level_values(0)
    grouped = grouped[grouped["Record_Type"].isin(["A", "B", "E", "Q", "S", "T", "X"])]
    return grouped


def get_rjo_daily_data_from_file(filename):
    df = read_rjo_daily_data(filename)
    log.debug(f"get_rjo_daily_data_from_file:: data read is: \n{df}")
    if df.empty:
        return df
    df = clean_rjo_data(df)
    log.debug(f"get_rjo_daily_data_from_file:: cleaned data: \n{df}")
    df = summarize_rjo_daily_data(df)
    log.debug(f"get_rjo_daily_data_from_file:: aggregated data: \n{df}")
    return df


def get_rjo_daily_data_since(year, month, day):
    data_dir = RJO_DATA_DIR.substitute(year=year)
    last_filename = get_rjo_daily_filename(year, month, day)
    log.info(
        f"get_rjo_daily_data_since::  last filename processed: {join(data_dir, last_filename)}"
    )
    files_to_load = [
        f for f in listdir(data_dir) if f > last_filename and "csvth1" in f
    ]
    log.debug(f"files to read: {files_to_load}")
    df = pd.DataFrame()
    for file in files_to_load:
        df = df.append(get_rjo_daily_data_from_file(join(data_dir, file)))
    return df


def get_unique_accounts_from_summary_through(summary_data, year, month):
    year = year * 100
    year_month = year + month
    return (
        summary_data[(summary_data.Trade_Month <= year_month)]
        .Account_Number.astype(int)
        .unique()
        .tolist()
    )


def get_commission_from_accounts(summary_data, accounts=[], month=None, day=None):
    """Total commissions for provided account in the provided month

    Get the total commission in the summary_data for all of the accounts in a given trading month.
    Used to get revenue from existing business (accounts) for a given month in the summary data

    Parameters
    ----------
    summary_data : [pd.DataFrame]
        a data frame containing at lease these columns: Trading_Month, Account_Number, Commission
    accounts : [list(int()]
        list of accounts to include in commission summary
    month : [int], optional
        trading month in the form 202005 for May 2020, by default None. If None, commission summary
        will be computed over the entire summary_data dataframe
    """
    if month:
        data = summary_data[
            (summary_data.Trade_Month == month)
            & summary_data.Account_Number.isin(accounts)
        ]
    else:
        data = summary_data[summary_data.Account_Number.isin(accounts)]
    if month and day:
        data = data[data.Trade_Day <= day]
    return data.Commission.sum()


def get_last_day(df, year, month):
    df_month = df[df.Trade_Month == year * 100 + month]
    return df_month.Trade_Day.max()


def update_rjo_agg_data(year):
    year = year or arrow.now().year
    agg_filename = f"./data/{year} RJO Agg Trade Data.csv"
    curr_ytd_summary = read_rjo_agg_file(agg_filename)
    year, month, day = last_agg_date_ymd(curr_ytd_summary)
    new_data = get_rjo_daily_data_since(year, month, day)
    curr_ytd_summary = curr_ytd_summary.append(new_data)
    curr_ytd_summary = curr_ytd_summary.sort_values(["Trade_Month", "Trade_Day"])
    curr_ytd_summary["Net_Commission"] = curr_ytd_summary.apply(
        lambda row: net_commission_from_row(row), axis=1
    )
    curr_ytd_summary.to_csv(agg_filename, index=False)
    return curr_ytd_summary


def calc_last_day_revenue(df, year, month):
    df_month = df[df.Trade_Month == year * 100 + month]
    last_day = df_month.Trade_Day.max()
    return calc_day_revenue(df, year, month, last_day)


def calc_day_revenue(df, year, month, day):
    df_month = df[df.Trade_Month == year * 100 + month]
    return df_month[df_month.Trade_Day == day].Commission.sum()


def calc_df_day_net_revenue(df, year, month, day):
    df_month = df[df.Trade_Month == year * 100 + month]
    return df_month[df_month.Trade_Day == day].Net_Commission.sum()


def calc_ytd_revenue(df, year, month, day):
    df_month = df[df.Trade_Month < year * 100 + month]
    prior_months = df_month.Commission.sum()
    df_month = df[df.Trade_Month == year * 100 + month]
    return prior_months + df_month[df_month.Trade_Day <= day].Commission.sum()


def calc_ytd_net_revenue(df, year, month, day):
    df_month = df[df.Trade_Month < year * 100 + month]
    prior_months = df_month.Net_Commission.sum()
    df_month = df[df.Trade_Month == year * 100 + month]
    return prior_months + df_month[df_month.Trade_Day <= day].Net_Commission.sum()


def calc_mtd_revenue(df, year, month, day):
    df_month = df[df.Trade_Month == year * 100 + month]
    return df_month[df_month.Trade_Day <= day].Commission.sum()


def calc_df_mtd_net_revenue(df, year, month, day):
    df_month = df[df.Trade_Month == year * 100 + month]
    return df_month[df_month.Trade_Day <= day].Net_Commission.sum()


def calc_mtd_contracts(df, year, month, day, acct_number=0):
    df_month = df[df.Trade_Month == year * 100 + month]
    if acct_number:
        df_month = df_month[df_month.Account_Number == acct_number]
    return df_month[df_month.Trade_Day <= day].Quantity.sum()


def get_rjo_daily_filename(year, month, day):
    month = f"{month:02d}"
    day = f"{day:02d}"
    return RJO_FILENAME_TEMPL.substitute(year=year, month=month, day=day)


def echo_stat(title, value):
    click.echo(f"{title:35}", nl=False)
    click.secho(f"{value:15.2f}", bold=True, fg="green")


@click.command()
@click.option("--year", default=arrow.utcnow().year, help="year of trades you want")
@click.option("--month", default=arrow.utcnow().month, help="month of trades you want")
@click.option("--day", default=None, help="day of trades you want")
def compute_stats(year, month, day):
    prev_year = year - 1
    curr_ytd_summary = update_rjo_agg_data(year)
    if not day:
        day = get_last_day(curr_ytd_summary, year, month)
    if not day:
        print(f"no data found for {year} {MONTHS[month]}")
    day = int(day)
    prev_year_summary = read_rjo_agg_file(f"./data/{prev_year} RJO Agg Trade Data.csv")
    existing_accounts = get_unique_accounts_from_summary_through(
        prev_year_summary, prev_year, month
    )
    mtd_comm_total = calc_mtd_revenue(curr_ytd_summary, year, month, day)
    py_mtd_comm_total = calc_mtd_revenue(prev_year_summary, prev_year, month, day)
    mtd_net_comm_total = calc_df_mtd_net_revenue(curr_ytd_summary, year, month, day)
    py_mtd_net_comm_total = calc_df_mtd_net_revenue(
        prev_year_summary, prev_year, month, day
    )
    mtd_contracts_total = calc_mtd_contracts(curr_ytd_summary, year, month, day)
    mtd_contracts_wrangler = calc_mtd_contracts(
        curr_ytd_summary, year, month, day, acct_number=84663
    )
    mtd_comm_from_existing = get_commission_from_accounts(
        curr_ytd_summary, existing_accounts, year * 100 + month, day
    )
    mtd_comm_from_new = mtd_comm_total - mtd_comm_from_existing
    day_comm_total = calc_day_revenue(curr_ytd_summary, year, month, day)
    py_day_comm_total = calc_day_revenue(prev_year_summary, prev_year, month, day)
    day_net_comm_total = calc_df_day_net_revenue(curr_ytd_summary, year, month, day)
    py_day_net_comm_total = calc_df_day_net_revenue(
        prev_year_summary, prev_year, month, day
    )
    days_trades = curr_ytd_summary[
        (curr_ytd_summary["Trade_Month"] == (year * 100) + month)
        & (curr_ytd_summary["Trade_Day"] == day)
    ]
    ytd_gross_commission = calc_ytd_revenue(curr_ytd_summary, year, month, day)
    py_ytd_gross_commission = calc_ytd_revenue(prev_year_summary, prev_year, month, day)
    ytd_net_commission = calc_ytd_net_revenue(curr_ytd_summary, year, month, day)
    py_ytd_net_commission = calc_ytd_net_revenue(
        prev_year_summary, prev_year, month, day
    )
    click.secho("Current Year MTD_Trades:", bold=True)
    max_rows = pd.options.display.max_rows
    pd.set_option("display.max_rows", None)
    click.echo(
        days_trades[
            [
                "Record_Type",
                "Account_Number",
                "Account_Name",
                "Contract",
                "Trade_Date",
                "Commission",
                "Quantity",
                "Net_Commission",
            ]
        ].sort_values("Commission", ascending=True)
    )
    pd.set_option("display.max_rows", max_rows)
    month = f"{month:02d}"
    day = f"{day:02d}"
    click.secho(f"Daily Trade/Commission Stats -- {year}-{month}-{day}:", bold=True)
    echo_stat("RJO Gross Revenue:", day_comm_total)
    echo_stat("RJO Net Revenue:", day_net_comm_total)
    click.secho(
        f"Daily Trade/Commission Stats -- {prev_year}-{month}-{day}:", bold=True
    )
    echo_stat("RJO Gross Revenue:", py_day_comm_total)
    echo_stat("RJO Net Revenue:", py_day_net_comm_total)
    click.secho("Current Year MTD Stats:", bold=True)
    echo_stat("Gross Revenue:", mtd_comm_total)
    echo_stat("Net Revenue:", mtd_net_comm_total)
    echo_stat("Gross Revenue Existing Clients:", mtd_comm_from_existing)
    echo_stat("Gross Revenue New Accounts", mtd_comm_from_new)
    echo_stat("Contracts Traded - Total:", mtd_contracts_total)
    echo_stat("Contracts Traded - Wrangler:", mtd_contracts_wrangler)
    click.secho("Prev Year MTD Stats:", bold=True)
    echo_stat("Gross Revenue:", py_mtd_comm_total)
    echo_stat("Net Revenue:", py_mtd_net_comm_total)
    click.secho("YTD Stats:", bold=True)
    echo_stat("Curr YTD Gross Revenue:", ytd_gross_commission)
    echo_stat("Prior YTD Gross Revenue:", py_ytd_gross_commission)
    echo_stat("Curr YTD Net Revenue:", ytd_net_commission)
    echo_stat("Prior YTD Net Revenue:", py_ytd_net_commission)


def foo():
    data_dir = RJO_DATA_DIR.substitute(year=2021)
    filename = RJO_FILENAME_TEMPL.substitute(year=2021, month=12, day=30)
    full_path = join(data_dir, filename)
    return get_rjo_daily_data_from_file(full_path)


if __name__ == "__main__":
    compute_stats()
