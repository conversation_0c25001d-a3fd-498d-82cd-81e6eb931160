from os import listdir
from os.path import join

import arrow
import pandas as pd

from src.constants import RJO_DATA_DIR, RJO_HEADERS, RJO_FILENAME_TEMPL, RJO_SUMMARY_FIELDS
from src.common.logging import log



def get_rjo_data(start, end=None):
    end = end or arrow.now()
    data_dir = RJO_DATA_DIR.substitute(year=start.year)
    first_filename = rjo_filename(start.year, start.month, start.day)
    last_filename = rjo_filename(end.year, end.month, end.day)
    files_to_read = [f for f in listdir(data_dir) if f > first_filename and f <= last_filename]
    raw = pd.DataFrame()
    for file in files_to_read:
        curr_file = join(data_dir, file)
        raw = raw.append(extract_raw_rjo_daily_data(curr_file))
    log.debug(f'\nget_rjo_data.raw=\n{raw}')
    return raw


def extract_raw_rjo_daily_data(filename):
    raw = read_raw_data(filename, RJO_HEADERS)
    raw = clean_raw_rjo_daily_data(raw)
    log.debug(f'\ncalling summarize with: raw = \n{raw.head()}')
    if not raw.empty:
        raw = summarize_raw_rjo_daily_data(raw)
    return raw

def read_raw_data(filename, headers):
    return pd.read_csv(filename, names=headers)
    

def clean_raw_rjo_daily_data(raw):
    raw.dropna(subset=['Account_Number'], inplace=True)
    raw['Account_Number'] = raw['Account_Number'].astype(int)
    if raw.empty:
        return raw
    raw['Trade_Month'] = raw.apply(trade_month_from_row, axis=1)
    raw['Trade_Day'] = raw.apply(trade_day_from_row, axis=1)
    log.debug(f'\nclean_raw_rjo_daily_data.raw = \n{raw.head()}')
    return raw


def summarize_raw_rjo_daily_data(raw):
    log.debug(f'\nsummarize input: raw=\n{raw.head()}')
    grouped = raw.groupby(RJO_SUMMARY_FIELDS).agg({'Commission': ['sum'], 'Quantity': ['sum']})
    
    grouped = grouped.reset_index()
    grouped.columns = grouped.columns.get_level_values(0)
    grouped = grouped[grouped['Record_Type'].isin(['A', 'B', 'E', 'Q', 'S', 'T', 'X'])]
    return grouped   


def trade_month_from_row(row):
    return int(row.Entry_Date/100)


def trade_day_from_row(row):
    return int(row.Entry_Date) - int(row.Entry_Date/100)*100


def rjo_filename(year, month, day):
    return RJO_FILENAME_TEMPL.substitute(
        year=year,
        month=f'{month:02d}',
        day=f'{day:02d}'
        )