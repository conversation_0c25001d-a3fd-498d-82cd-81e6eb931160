import arrow
import bisect

from src.xdomain.constants import FEE_REGIMES, FEE_SCHEDULE
from src.xdomain.value_objects import TradeDate


def get_fee_schedule(trade_date: TradeDate):
    start_dates = sorted(FEE_REGIMES.keys())
    date_to_find = trade_date.format('YYYY-MM-DD')
    start_date_index = bisect.bisect_right(start_dates, date_to_find)
    if start_date_index == 0:
        return FEE_REGIMES.get(start_dates[0], FEE_SCHEDULE["default"])
    else:
        return FEE_REGIMES.get(start_dates[start_date_index-1], FEE_SCHEDULE["default"])


def rjo_trade_date_to_date(rjo_trade_date: int) -> arrow.Arrow:
    date_str = str(rjo_trade_date)
    return arrow.get(f'{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}')
