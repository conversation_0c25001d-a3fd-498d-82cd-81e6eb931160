import click
import pandas as pd


def echo_stat(title, value):
    click.echo(f"{title:35}", nl=False)
    click.secho(f"{value:15.2f}", bold=True, fg="green")


def display_stats(df, caption):
    total_contracts = df["Quantity"].sum()
    total_trades = df[df.Commission != 0.0].Quantity.sum()
    wrangler_contracts = df[
        (df.Account_Number == 84663) & (df.Commission != 0.0)
    ].Quantity.sum()
    igs_ice_contracts = df[
        (df.Account_Number.isin([84478, 84578]))
        & (df.Market == "ICE")
        & (df.Commission != 0)
    ].Quantity.sum()
    two_dollar_contracts = total_trades - wrangler_contracts - igs_ice_contracts
    click.secho(caption, fg="green", bold=True)
    echo_stat("   Gross Commission", df["Commission"].sum() * -1)
    echo_stat("   Net Commission", df["Net_Commission"].sum() * -1)
    echo_stat("   Contracts at Regular Fee", two_dollar_contracts)
    echo_stat("   IGS/ICE Contracts", igs_ice_contracts)
    echo_stat("   Wrangler Contracts", wrangler_contracts)
    echo_stat("   Total Contracts", total_contracts)


def display_df(df, sort_col=None, title="Sorted Data", show_index=False):
    if sort_col is not None:
        asc = sort_col[0] != "-"
        sort_col = sort_col[1:] if sort_col[0] in ["-", "+"] else sort_col
        sort_col = [sort_col]
        if "Prev_Net_Commission" in df.columns:
            sort_col.append("Prev_Net_Commission")
        df = df.sort_values(by=sort_col, ascending=asc)
    max_rows = pd.options.display.max_rows
    pd.set_option("display.max_rows", None)
    click.echo(title)
    click.echo(df.to_string(index=show_index))
    pd.options.display.max_colwidth = max_rows
