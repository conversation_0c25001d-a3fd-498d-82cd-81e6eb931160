from ftplib import FTP, error_perm
from typing import Any, Optional

from loguru import logger


class BoxArchive(object):
    host: Optional[str] = None
    username: Optional[str] = None
    password: Optional[str] = None
    _client: Any = None

    def __init__(self, **kwargs):
        for attr in ["host", "username", "password"]:
            setattr(self, attr, kwargs.get(attr, None))
        logger.info(
            f"BoxArchive created with host={self.host}, username={self.username}"
        )

    @property
    def logged_in(self):
        try:
            curr_directory = self._client.pwd()
        except Exception:
            curr_directory = None
        return curr_directory is not None

    def login(self, username=None, password=None):
        logger.info(f"BoxArchive username={self.username}")
        user = username or self.username
        passwd = password or self.password
        if self.host:
            logger.info(f"BoxArchive logging in as {user}")
            self._client = FTP(self.host)
        else:
            logger.error("BoxArchive login failed:  host string missing")
        try:
            self._client.login(user=user, passwd=passwd)
        except Exception as e:
            logger.error(f"BoxArchive login failed: {e}")

    def close(self):
        if self.logged_in:
            self._client.close()

    def cwd(self, locator):
        self._client.cwd(locator)

    def delete(self, filename):
        return self._client.delete(filename)

    def items(self, locator):
        items_list = {}
        try:
            self._client.cwd(locator)
            for item in self._client.mlsd():
                items_list[item[0]] = item[1]
        except error_perm as e:
            logger.error(f"error_perm is {e}")
            logger.error(f"locator {locator} not found")
        return items_list

    def putfo(self, filename, file):
        try:
            file.seek(0)
            self._client.storbinary(f"STOR {filename}", file)
            return "success"
        except Exception as e:
            logger.error(f"BoxArchive putfo error: {e}")
            return f"error saving file {filename}: {e}"
