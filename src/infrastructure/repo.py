import re
from os import listdir
from os.path import join, isfile

import arrow
import pandas as pd
from loguru import logger as log

from src.constants import (
    FEE_SCHEDULE,
    ICE,
    NYMEX,
    RJO_DATA_DIR,
    RJO_FILENAME_TEMPL,
    RJO_HEADERS,
)
from src.commands.actions import get_fee_schedule, rjo_trade_date_to_date


def get_rjo_agg_data(year):
    agg_data = update_rjo_agg_data(year)
    agg_data["Record_Date"] = agg_data.apply(
        lambda row: row.Trade_Month * 100 + row.Trade_Day, axis=1
    )
    return agg_data


def update_rjo_agg_data(year):
    year = year or arrow.now().year
    agg_filename = f"./data/{year} RJO Agg Trade Data.csv"
    curr_ytd_summary = read_rjo_agg_file(agg_filename)
    year, month, day = last_agg_date_ymd(curr_ytd_summary)
    new_data = get_rjo_daily_data_since(year, month, day)
    curr_ytd_summary = curr_ytd_summary.append(new_data)
    curr_ytd_summary = curr_ytd_summary.sort_values(["Trade_Month", "Trade_Day"])
    curr_ytd_summary["Net_Commission"] = curr_ytd_summary.apply(
        lambda row: net_commission_from_row(row), axis=1
    )
    curr_ytd_summary.to_csv(agg_filename, index=False)
    return curr_ytd_summary


def read_rjo_daily_data(filename):
    if not isinstance(filename, str):
        raise ValueError("Filename must be a string")
    if not isfile(filename):
        log.info(f"File not found: {filename}...creating an empty DataFrame")
        return pd.DataFrame(columns=RJO_HEADERS)

    df = pd.read_csv(filename, names=RJO_HEADERS, engine="python")
    df.dropna(subset=["Account_Number"], inplace=True)
    df["Account_Number"] = df["Account_Number"].astype(int)
    return df


def read_rjo_agg_file(filename):
    raw = pd.DataFrame(columns=RJO_HEADERS)
    try:
        raw = pd.read_csv(filename)
        raw.dropna(subset=["Account_Number"], inplace=True)
        raw["Account_Number"] = raw["Account_Number"].astype(int)
        raw["Trade_Month"] = raw["Trade_Month"].astype(int)
        raw["Trade_Day"] = raw["Trade_Day"].astype(int)
    except FileNotFoundError:
        log.warning(f"File not found: {filename}")
    return raw


def clean_rjo_data(raw):
    raw["Account_Number"] = raw["Account_Number"].astype(int)
    raw["Trade_Month"] = raw.apply(trade_month_from_row, axis=1)
    raw["Trade_Day"] = raw.apply(trade_day_from_row, axis=1)
    raw["Market"] = raw.apply(trade_market_from_row, axis=1)
    raw["Net_Commission"] = raw.apply(net_commission_from_row, axis=1)
    log.debug(f"raw: {raw}")
    return raw


def summarize_rjo_daily_data(raw):
    grouped = raw.groupby(
        [
            "Record_Type",
            "Account_Number",
            "Account_Name",
            "Contract",
            "Trade_Date",
            "Trade_Month",
            "Trade_Day",
            "Market",
            "Entry_Date",
        ]
    ).agg({"Commission": ["sum"], "Quantity": ["sum"], "Net_Commission": ["sum"]})
    grouped = grouped.reset_index()
    grouped.columns = grouped.columns.get_level_values(0)
    grouped = grouped[grouped["Record_Type"].isin(["A", "B", "E", "Q", "S", "T", "X"])]
    return grouped


def last_agg_date_ymd(df):
    if df.empty:
        return arrow.utcnow().year, 1, 0
    year_month = df.Trade_Month.max()
    day = df[df.Trade_Month == year_month].Trade_Day.max()
    year = int(year_month / 100)
    month = int(year_month - year * 100)
    day = int(day)
    return year, month, day


def get_rjo_daily_data_since(year, month, day):
    data_dir = RJO_DATA_DIR.substitute(year=year)
    last_filename = get_rjo_daily_filename(year, month, day)
    log.debug(
        f"get_rjo_daily_data_since::  last filename processed: {join(data_dir, last_filename)}"
    )
    files_to_load = [
        f for f in listdir(data_dir) if f > last_filename and "csvth1" in f
    ]
    log.debug(f"files to read: {files_to_load}")
    df = pd.DataFrame()
    for file in files_to_load:
        df = df.append(get_rjo_daily_data_from_file(join(data_dir, file)))
    return df


def net_commission_from_row(record):
    trade_date = rjo_trade_date_to_date(record.Entry_Date)
    fee_schedule = get_fee_schedule(trade_date)
    account_fee_structure = fee_schedule.get(
        record.Account_Number, FEE_SCHEDULE["default"]
    )
    default_fee = fee_schedule["default"][NYMEX]
    per_trade_fee = account_fee_structure.get(record["Market"], default_fee)
    return (
        record["Commission"] + (record["Quantity"] * per_trade_fee)
        if record["Commission"]
        else 0
    )


def trade_month_from_row(row):
    return int(row.Entry_Date / 100)


def trade_day_from_row(row):
    return int(row.Entry_Date) - int(row.Entry_Date / 100) * 100


def trade_market_from_row(row):
    mkt = NYMEX
    z = re.match(r".*[A-Z]{3}\s\d{2}\s([A-Z]{3})\s", row.Security_Desc_1)
    if z:
        mkt = z.groups(1)[0]
    return mkt if mkt in [NYMEX, ICE] else NYMEX


def get_rjo_daily_filename(year, month, day):
    month = f"{month:02d}"
    day = f"{day:02d}"
    return RJO_FILENAME_TEMPL.substitute(year=year, month=month, day=day)


def get_rjo_daily_data_from_file(filename):
    df = read_rjo_daily_data(filename)
    log.debug(f"get_rjo_daily_data_from_file:: data read is: \n{df}")
    if df.empty:
        return df
    df = clean_rjo_data(df)
    log.debug(f"get_rjo_daily_data_from_file:: cleaned data: \n{df}")
    df = summarize_rjo_daily_data(df)
    log.debug(f"get_rjo_daily_data_from_file:: aggregated data: \n{df}")
    return df
