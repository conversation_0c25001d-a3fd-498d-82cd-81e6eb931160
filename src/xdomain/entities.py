from dataclasses import dataclass, field

import arrow

from src.xdomain.value_objects import (
    BuySell,
    Currency,
    Market,
    Price,
    Source,
    Symbol,
    Volume)



@dataclass
class Trade:
    trade_date: arrow.Arrow
    buy_sell: BuySell
    quantity: Volume
    price: Price
    symbol: Symbol
    contract: str
    account_number: str
    account_name: str
    commission: Currency
    net_commission: Currency
    market: Market
    source: Source
    record_date: arrow.Arrow =  field(default_factory=arrow.now)
