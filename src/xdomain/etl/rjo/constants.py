import os
from string import Template
from typing import List


# data fields needed to transform rjo raw data to final trade data form
#  many of these fields will be in the trade data dataframe, but some (like
#  Security_Desc_1, and Bloomberg_Exchange or nessecary for some of the
#  transformation funcitons that produce "Market" values) will not be in the
#  trade data dataframe.
EXTRACTED_DATA_FIELDS = [
    "Record_Type",
    "Account_Number",
    "Account_Name",
    "Security_Desc_1",
    "Contract",
    "Quantity",
    "Trade_Date",
    "Entry_Date",
    "Commission",
    "Security_Subtype",
    "Bloomberg_Exchange",
]

TRADE_DATA_FIELDS = [
    "Record_Type",
    "Account_Number",
    "Account_Name",
    "Contract",
    "Contract_Type",
    "Quantity",
    "Trade_Date",
    "Market",
    "Entry_Date",
    "Commission",
    "Net_Commission",
]
TRADE_RECORD_TYPES: List[str] = ["A", "B", "E", "Q", "S", "T", "X"]
MKT_TZ = "America/New_York"

USER_ROOT = os.getenv("HOME")
RJO_DATA_DIR = Template(
    # f"{USER_ROOT}/Library/CloudStorage/Box-Box/Corp Docs (internal access only)/TeamLevine dba Powerhouse/accounting/$year/trade statistics"
    f"{USER_ROOT}/Box Sync/Corp Docs (internal access only)/TeamLevine dba Powerhouse/accounting/$year/trade statistics"
)
AGG_DATA_DIR = Template("./data")
RJO_FILENAME_TEMPL = Template("POWERHOUSE_csvth1_dth1_$year$month$day.csv")
RJO_HEADERS = [
    "Record_Type",
    "Firm_Code",
    "Office",
    "Salesman",
    "Account_Number",
    "Account_Class",
    "Account_Subclass",
    "Account_Type",
    "Card_Number",
    "Buy_Sell",
    "Quantity",
    "Security_Desc_1",
    "Formatted_Trade_Price",
    "Trade_Date",
    "Open_Close",
    "Contract_Month",
    "Contract_Day",
    "Security_Type",
    "Security_Subtype",
    "Option_Strike_Price",
    "Option_Expire_Date",
    "Subaccount_ID",
    "Broker",
    "Spread",
    "Account_Name",
    "Source_Account_Number",
    "Settlement_Date",
    "Exchange",
    "Trading_Exchange",
    "Contract",
    "Contract_Symbol",
    "Product_Type",
    "Trade_Price",
    "Trade_Calc_Control",
    "Comment_1",
    "Comment_2",
    "Executing_Broker",
    "Give_Up",
    "Give_Up_Firm",
    "Buy_Quantity",
    "Sell_Quantity",
    "Account_Type_Currency",
    "Commission",
    "Clearing_Fee",
    "Exchange_Fee",
    "NFA_Fee",
    "Global_Desk_Charge",
    "RJO_Trans_Fee",
    "IB_Trans_Fee",
    "Charge_Amount_Give_Up",
    "Charge_Amount_Brokerage",
    "Charge_Amount_Other",
    "Total_Net_Charge",
    "Multiplication_Factor",
    "Market_Value",
    "Close_Price",
    "Underlying_Close_Price",
    "CUSIP_Code",
    "Commission_Account_Type",
    "Charge_Clearing_Account_Type",
    "Charge_Exchange_Account_Type",
    "Charge_NFA_Account_Type",
    "Charge_Global_Desk_Account_Type",
    "Charge_RJO_Trans_Account_Type",
    "Charge_IB_Trans_Account_Type",
    "Charge_Give_Up_Account_Type",
    "Charge_Brokerage_Account_Type",
    "Charge_Other_Account_Type",
    "Tracer_Number",
    "Order_Number",
    "Bloomberg_Root",
    "Bloomberg_Exchange",
    "Bloomberg_Market_Sector",
    "Option_Premium",
    "Security_Desc_2",
    "Security_Desc_3",
    "Trade_Desc_1",
    "Trade_Desc_2",
    "Trade_Desc_3",
    "Delta_Factor",
    "Product_Currency",
    "Commission_Field_Define_Code",
    "Charge_Clearing_Field_Define_Code",
    "Charge_Exchange_Field_Define_Code",
    "Charge_NFA_Field_Define_Code",
    "Charge_Global_Desk_Field_Define_Code",
    "Charge_RJO_Field_Define_Code",
    "Charge_IB_Trans_Field_Define_Code",
    "Charge_Give_Up_Field_Define_Code",
    "Charge_Brokerage_Field_Define_Code",
    "Charge_Other_Field_Define_Code",
    "Charge_Clearing_Field_Usage_Code",
    "Charge_Exchange_Field_Usage_Code",
    "Charge_NFA_Field_Usage_Code",
    "Charge_Global_Desk_Field_Usage_Code",
    "Charge_RJO_Field_Usage_Code",
    "Charge_IB_Trans_Field_Usage_Code",
    "Charge_Give_Up_Field_Usage_Code",
    "Charge_Brokerage_Field_Usage_Code",
    "Charge_Other_Field_Usage_Code",
    "Entry_Date",
    "Round_Half_Turn",
]
