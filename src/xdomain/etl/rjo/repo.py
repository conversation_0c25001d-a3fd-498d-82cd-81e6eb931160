import os
from dataclasses import dataclass, field
import glob
from pathlib import Path
from string import Template
from typing import List

import arrow
from loguru import logger as log
import pandas as pd

from src.xdomain.etl.rjo.constants import (
    AGG_DATA_DIR,
    RJO_DATA_DIR,
    RJO_FILENAME_TEMPL,
    RJO_HEADERS,
    TRADE_DATA_FIELDS,
)
from src.xdomain.etl import rjo


@dataclass
class RJORepo:
    data_dir_template: Template = field(default_factory=lambda: RJO_DATA_DIR)
    daily_filename_template: Template = field(
        default_factory=lambda: RJO_FILENAME_TEMPL
    )
    agg_dir_template: Template = field(default_factory=lambda: AGG_DATA_DIR)
    agg_filename_template: Template = field(
        default_factory=lambda: Template("$year Agg Trade Data.csv")
    )

    def _read_agg_data(self, year: int) -> pd.DataFrame:
        """read the aggregate trade data from the given filename and return a DataFrame

        Args:
            year (int): _description_
        """
        dir_name = self.agg_dir_template.substitute(year=year)
        filename = f"{dir_name}/{self.agg_filename_template.substitute(year=year)}"
        if not Path(filename).is_file():
            data = pd.DataFrame(columns=TRADE_DATA_FIELDS)
            return data
        data = read_csv_data(filename)
        data["Entry_Date"] = data["Entry_Date"].astype(int)
        data["Quantity"] = data["Quantity"].astype(int)
        data["Commission"] = data["Commission"].astype(float)
        data["Net_Commission"] = data["Net_Commission"].astype(float)
        data["Trade_Date"] = data["Trade_Date"].astype(int)
        data["Account_Number"] = data["Account_Number"].astype(int)
        return data

    def _read_daily_data(self, year: int, month: int, day: int) -> pd.DataFrame:
        """read the aggregate trade data from the given filename and return a DataFrame

        Args:
            year (int): _description_
            month (int): _description_
            day (int): _description_
        """
        filename = get_daily_filename_for_year_month_day(year, month, day)
        return read_csv_data(filename, headers=RJO_HEADERS)

    def _daily_files_since(self, year: int, month: int, day: int) -> List[str]:
        """return a list of filenames for the daily files since the given date"""
        dir_name = self.data_dir_template.substitute(year=year)
        last_daily_file = get_daily_filename_for_year_month_day(
            year,
            month,
            day,
            dir_template=self.data_dir_template,
            filename_template=self.daily_filename_template,
        )
        files = sorted(glob.glob(f"{dir_name}/POWERHOUSE*_{year}*.csv"))
        return [f for f in files if f > last_daily_file]

    def _aggregate_daily_data_file(self, filename: str) -> pd.DataFrame:
        data = read_csv_data(filename, headers=RJO_HEADERS)
        data = rjo.extract(data)
        data = rjo.transform(data)
        return rjo.aggregate(data)

    def get_day_summary(self, year: int, month: int, day: int) -> pd.DataFrame:
        """return a summary of the daily trade data for the given year/month/day"""
        filename = get_daily_filename_for_year_month_day(year, month, day)
        return self._aggregate_daily_data_file(filename)

    def get_trade_stats(self, year: int, month: int = 0, day: int = 0) -> pd.DataFrame:
        agg_data = self._read_agg_data(year)
        as_of_date = year * 10000 + month * 100 + day
        return agg_data[agg_data["Entry_Date"] <= as_of_date]

    def save(self, data: pd.DataFrame, filename: str) -> pd.DataFrame:
        try:
            data.to_csv(filename, index=False)
        except Exception as e:
            log.error(f"Error saving data to {filename}: {e}")
            raise e
        return data

    def save_stats(self, data: pd.DataFrame, year: int) -> str:
        """save the daily trade data to the given year"""
        dir_name = self.agg_dir_template.substitute(year=year)
        filename = f"{dir_name}/{self.agg_filename_template.substitute(year=year)}"
        self.save(data, filename)
        log.info(f"Saved aggregate trade data to {filename}")
        return filename

    def update_stats(self, year: int = 0, force: bool = False) -> pd.DataFrame:
        """updates the aggregate data file for the given year, or for the
        current year if no year is provided.  If the file does not exist, then
        it is created with the headers from TRADE_DATA_FIELDS, and any aggregated
        data for any days so far in the year. If the file does exist, then
        if the last Entry_Date is before the last day of the year (or the current
        date if no year is provided) then the data for any days not yet in the
        aggregate file. If the last Entry_Date is after the last day of the year,
        then the aggregate file is not updated.

        if the force flag is set to True, then the aggregate file is updated regardless
        and the data is overwritten to the aggregate file
        """
        year = year or arrow.now().year
        if not year or year < 2013:
            raise ValueError("year is required to be greateer than 2013")
        agg_data = self._read_agg_data(year)
        last_date = agg_data.Entry_Date.max()
        last_date = last_date if last_date > 0 else year * 10000 + 100 + 0
        year = last_date // 10000
        month = last_date // 100 - (year * 100)
        day = last_date - (year * 10000) - (month * 100)
        files_to_add = self._daily_files_since(year, month, day)
        for file in files_to_add:
            log.info(f"processing data from {Path(file).name}")
            data = self._aggregate_daily_data_file(file)
            log.info(f"adding data from {Path(file).name}")
            agg_data = pd.concat([agg_data, data], ignore_index=True)
            # agg_data = agg_data.append(data, ignore_index=True)  # type: ignore
            log.info(f"finished processing data from {Path(file).name}")
        return agg_data


def get_daily_data_file(year: int, month: int = 0, day: int = 0) -> pd.DataFrame:
    """get the aggregate trade data for the given year, or year/month, or
    year/month/day.  If only year is provided, then the entire year is returned.
    If only year/month is provided, then the entire month is returned (if it exists);
    and all records with a record_date <= the last day of the month are returned.

    Args:
        year (int): _description_
        month (int): _description_
        day (int): _description_
    """
    agg_data_filename = get_daily_filename_for_year_month_day(year, month, day)
    trade_data = read_csv_data(agg_data_filename)
    return trade_data


def read_csv_data(filename: str, headers: List[str] = []) -> pd.DataFrame:
    """read the aggregate trade data from the given filename and return a DataFrame

    Args:
        filename (str): _description_
    """
    try:
        raw = pd.read_csv(filename, names=headers) if headers else pd.read_csv(filename)
    except FileNotFoundError:
        print(f"File not found: {filename}...creating an empty DataFrame")
        raw = pd.DataFrame(columns=headers) if headers else pd.DataFrame()
    return raw


### Utilities


def get_daily_filename_for_year_month_day(
    year: int,
    month: int,
    day: int,
    dir_template: Template = RJO_DATA_DIR,
    filename_template: Template = RJO_FILENAME_TEMPL,
) -> str:
    if not year or year < 2013:
        raise ValueError("year is required to be greateer than 2013")
    if month < 1 or month > 12:
        raise ValueError("month is required to be between 1 and 12")
    if day < 0 or day > 31:
        raise ValueError("day is required to be between 1 and 31")
    data_dir = dir_template.substitute(year=str(year))
    filename = filename_template.substitute(
        year=str(year), month=str(month).zfill(2), day=str(day).zfill(2)
    )
    return f"{data_dir}/{filename}"


def write_data(data: pd.DataFrame, filename: str):
    data.to_csv(filename, index=False)
    return filename
