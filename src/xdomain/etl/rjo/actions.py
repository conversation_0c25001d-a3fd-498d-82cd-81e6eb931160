import re

from loguru import logger as log

from src.constants import CME, NYMEX, ICE, FEE_HISTORY


def trade_market_from_row(row) -> str:
    mkt = NYMEX
    if z := re.match(r".*[A-Z]{3}\s\d{2}\s([A-Z]{3})\s", row.Security_Desc_1):
        mkt = str(z.groups(1)[0])
    mkt = mkt if mkt in [CME, NYMEX, ICE] else row.Bloomberg_Exchange
    return mkt if mkt in [CME, NYMEX, ICE] else "UNKNOWN"


def net_commission_from_row(row) -> float:
    """will be used to calculate commission"""
    market = trade_market_from_row(row)
    clearing_rate = clearing_cost_for_date_acct_mkt(
        row.Entry_Date, row.Account_Number, market
    )
    return row.Commission - (row.Quantity * clearing_rate)


def commission_from_row(row) -> float:
    return row.Commission * -1


def contract_type_from_row(row) -> str:
    """will be used to determine the type of contract"""
    quantity = row.Quantity
    if not quantity:
        return "N"
    return row.Security_Subtype if row.Security_Subtype in ["C", "P"] else "F"


def clearing_cost_for_date_acct_mkt(date: int, account: int, mkt: str) -> float:
    """return the clearing fee for the given account number and date.  Uses
    FEE_HISTORY to determine the fee by first getting the fee regime in place on
    the given data, then finding the fee for the given account number and market

    Args:
        acct_num (int): rjo account number as int
        date (int): int in YYYYMMDD format NOTE this should be the entry_date
                    of the trade as commission is calculated on the entry date
        mkt (str): market code [ICE, NYMEX]

    Returns:
        float: the clearing fee for the given account number and date
    """
    fee_schedule_dates = sorted(FEE_HISTORY.keys(), reverse=True)
    for fee_schedule_date in fee_schedule_dates:
        if date >= fee_schedule_date:
            fee_schedule = FEE_HISTORY[fee_schedule_date]
            default_schedule = fee_schedule[0]
            account_fee_structure = fee_schedule.get(account, default_schedule)
            return account_fee_structure.get(mkt, default_schedule[mkt])
    return 0.0


def last_entry_date(df):
    return df.Entry_Date.max()
