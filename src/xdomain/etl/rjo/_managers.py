from pandas import DataFrame

from src.xdomain.etl.rjo.constants import (
    EXTRACTED_DATA_FIELDS,
    TRADE_DATA_FIELDS,
    TRADE_RECORD_TYPES,
)
from src.xdomain.etl.rjo.actions import (
    contract_type_from_row,
    net_commission_from_row,
    trade_market_from_row,
)


def extract(raw: DataFrame) -> DataFrame:
    """extract the raw trade data from a dataframe read from RJO daily
    data file and return a DataFrame with only the fields needed for
    creating trade records"""
    data = raw.dropna(subset=["Account_Number"])
    data["Account_Number"] = data["Account_Number"].astype(int)
    data = data[EXTRACTED_DATA_FIELDS]
    data = data[data["Record_Type"].isin(TRADE_RECORD_TYPES)]
    return data


def transform(raw: DataFrame) -> DataFrame:
    """transform the raw trade data from a dataframe read from RJO daily
    data file and return a DataFrame with only the fields needed for
    creating trade records"""
    data = raw.copy()
    if data.empty:
        data["Market"] = []
        data["Net_Commission"] = []
        data["Contract_Type"] = []
        return data
    data["Account_Number"] = data["Account_Number"].astype(int)
    data["Trade_Date"] = data["Trade_Date"].astype(int)
    data["Entry_Date"] = data["Entry_Date"].astype(int)
    # must apply Market transform BEFORE Net_Commission transform, as net commission
    # is dependent on the Market value to look up clearing rates for trades prior to
    # 2024-01-01
    data["Market"] = data.apply(lambda row: trade_market_from_row(row), axis=1)
    data["Commission"] = data["Commission"] * -1
    data["Net_Commission"] = data.apply(
        lambda row: net_commission_from_row(row), axis=1
    )
    data["Contract_Type"] = data.apply(lambda row: contract_type_from_row(row), axis=1)
    data = data[TRADE_DATA_FIELDS]
    return data


def aggregate(raw: DataFrame) -> DataFrame:
    """aggregate the transformed trade data from a dataframe read from RJO daily
    data file and return a DataFrame showing commission, quantity, and net_commission
    summarized by record_type, entry_date, account_number, contract, contract_type,
    and market"""
    data = raw.copy()
    grouped = data.groupby(
        [
            "Record_Type",
            "Account_Number",
            "Account_Name",
            "Contract",
            "Trade_Date",
            "Market",
            "Entry_Date",
            "Contract_Type",
        ]
    ).agg({"Commission": ["sum"], "Quantity": ["sum"], "Net_Commission": ["sum"]})
    grouped = grouped.reset_index()
    grouped.columns = grouped.columns.get_level_values(0)
    return grouped
