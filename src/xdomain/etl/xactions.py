from os.path import isfile

from pandas import DataFrame, read_csv
from arrow import Arrow
from loguru import logger as log

from src.constants import (
    RJO_HEADERS,
    RJO_DATA_DIR,
    RJO_FILENAME_TEMPL,
    TRADE_DATA_FIELDS,
)

AGG_HEADERS: list = []


def read_agg_trade_data(filename) -> DataFrame:
    """read the aggregate trade data from the given filename and return a DataFrame

    Args:
        filename (str): _description_
    """
    raw = DataFrame()
    try:
        raw = read_csv(filename)
    except FileNotFoundError:
        log.info(f"File not found: {filename}...creating an empty DataFrame")
        raw = DataFrame(columns=AGG_HEADERS)
    return raw


def get_trade_data(year: int, month: int = 0, day: int = 0) -> DataFrame:
    """get the aggregate trade data for the given year, or year/month, or
    year/month/day.  If only year is provided, then the entire year is returned.
    If only year/month is provided, then the entire month is returned (if it exists);
    and all records with a record_date <= the last day of the month are returned.

    Args:
        year (int): _description_
        month (int): _description_
        day (int): _description_
    """
    if not year or year < 2013:
        raise ValueError("year is required to be greateer than 2013")
    if month and month < 1 or month > 12:
        raise ValueError("month is required to be between 1 and 12")
    if day and day < 1 or day > 31:
        raise ValueError("day is required to be between 1 and 31")
    if year and month and day:
        target_date = year * 10000 + month * 100 + day
    elif year and month:
        target_date = year * 10000 + month * 100 + 31
    elif year and year > 2013:
        target_date = year * 10000 + 1231
    agg_data_filename = f"./data/{year} RJO Agg Trade Data.csv"
    trade_data = read_agg_trade_data(agg_data_filename)
    return trade_data


def read_rjo_data_file(filename, headers=RJO_HEADERS) -> DataFrame:
    if not isinstance(filename, str):
        raise ValueError("Filename must be a string")
    if not isfile(filename):
        log.info(f"File not found: {filename}...creating an empty DataFrame")
        return DataFrame(columns=headers)

    df = read_csv(filename, names=headers, engine="python")
    return df


def extract_rjo_raw_data(raw: DataFrame) -> DataFrame:
    """clean the raw trade data from the RJO and return a DataFrame"""
    data = raw.dropna(subset=["Account_Number"])
    data["Account_Number"] = data["Account_Number"].astype(int)
    data = data[TRADE_DATA_FIELDS]
    return data


if __name__ == "__main__":
    filename = RJO_FILENAME_TEMPL.substitute(year="2024", month="01", day="30")
    path = RJO_DATA_DIR.substitute(year="2024")
    pathname = f"{path}/{filename}"
    day_data = read_rjo_data_file(pathname)
    print(day_data.head())
