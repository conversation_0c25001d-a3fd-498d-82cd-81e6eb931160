from dataclasses import dataclass
from decimal import Decimal
from enum import Enum
from typing import Optional, Union

from arrow import Arrow

from src.constants import CME_MONTHS, MIN_EXP_YEAR, MAX_EXP_YEAR

Volume = Decimal
Price = Decimal
Currency = Decimal
Market = str
PutCall = str
Source = str
TradeDate = Arrow

class Markets:
    NYMEX = 'NYMEX'
    ICE = 'ICE'


class Sources:
    RJO = 'RJO'
    RISK_CANOPY = 'RiskCanopy'


@dataclass
class Symbol:
    contract: str
    month: Union[int, str]
    year: int
    put_call: Optional[PutCall] = None
    strike: Optional[Price] = None

    def __post_init__(self):
        if self.put_call and not self.strike:
            raise ValueError("put_call requires strike")
        if self.strike and not self.put_call:
            raise ValueError("strike requires put_call")
        if self.is_option:
            if not isinstance(self.strike, (float, int, Price)):
                raise ValueError("strike must be an int or float")
            if self.strike <= 0:
                raise ValueError("strike must be greater than 0")
            if self.put_call.upper() not in ["C", "P"]:
                raise ValueError("put_call must be 'C' or 'P'")
        if not self._validate_month(self.month):
            raise ValueError("month must be btw 1-12, or a CME Month Code")
        if not (MIN_EXP_YEAR <= self.year <= MAX_EXP_YEAR):
            raise ValueError(f"year must be between {MIN_EXP_YEAR} and {MAX_EXP_YEAR}")
        if isinstance(self.month, str):
            self.month = CME_MONTHS.index(self.month.upper()) + 1

    def _validate_month(self, month: Union[int, str]) -> bool:
        if isinstance(month, int) and (1 <= month <= 12):
            return True
        return isinstance(month, str) and month.upper() in CME_MONTHS


    @property
    def expiration(self):
        return f"{CME_MONTHS[self.month - 1]}{self.year-2000}"

    @property
    def is_option(self):
        return self.put_call is not None

    def __str__(self):
        if self.is_option:
            return f"{self.contract}{self.expiration}{self.put_call}{self.strike}"
        return f"{self.contract}{self.expiration}"


class BuySell(Enum):
    BUY = "B"
    SELL = "S"


