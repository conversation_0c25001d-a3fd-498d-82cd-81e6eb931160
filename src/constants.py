import os
from string import Template
from typing import Dict, Union


CME_MONTHS = ["F", "G", "H", "J", "K", "M", "N", "Q", "U", "V", "X", "Z"]
MIN_EXP_YEAR = 2010
MAX_EXP_YEAR = 2100

RJO_AGG_STATS_FILE_HEADERS = [
    "Record_Type",
    "Account_Number",
    "Account_Name",
    "Contract",
    "Trade_Month",
    "Trade_Day",
    "Commission",
    "Quantity",
]
STAT_FIELDS = [
    "Record_Type",
    "Account_Number",
    "Account_Name",
    "Contract",
    "Trade_Date",
    "Security_Desc_1",
    "Quantity",
    "Commission",
    "Trade_Month",
    "Trade_Day",
]
RJO_SUMMARY_FIELDS = [
    "Record_Type",
    "Account_Number",
    "Account_Name",
    "Contract",
    "Trade_Date",
    "Trade_Month",
    "Trade_Day",
]
TRADE_DATA_FIELDS = [
    "Record_Type",
    "Account_Number",
    "Account_Name",
    "Security_Desc_1",
    "Contract",
    "Quantity",
    "Trade_Date",
    "Entry_Date",
    "Commission",
    "Security_Subtype",
    "Bloomberg_Exchange",
]

USER_ROOT = os.getenv("HOME")
RJO_DATA_DIR = Template(
    f"{USER_ROOT}/Library/CloudStorage/Box-Box/Corp Docs (internal access only)/TeamLevine dba Powerhouse/accounting/$year/trade statistics"
)

RJO_FILENAME_TEMPL = Template("POWERHOUSE_csvth1_dth1_$year$month$day.csv")
RECORD_TYPE = "Record_Type"

RJO_HEADERS = [
    RECORD_TYPE,
    "Firm_Code",
    "Office",
    "Salesman",
    "Account_Number",
    "Account_Class",
    "Account_Subclass",
    "Account_Type",
    "Card_Number",
    "Buy_Sell",
    "Quantity",
    "Security_Desc_1",
    "Formatted_Trade_Price",
    "Trade_Date",
    "Open_Close",
    "Contract_Month",
    "Contract_Day",
    "Security_Type",
    "Security_Subtype",
    "Option_Strike_Price",
    "Option_Expire_Date",
    "Subaccount_ID",
    "Broker",
    "Spread",
    "Account_Name",
    "Source_Account_Number",
    "Settlement_Date",
    "Exchange",
    "Trading_Exchange",
    "Contract",
    "Contract_Symbol",
    "Product_Type",
    "Trade_Price",
    "Trade_Calc_Control",
    "Comment_1",
    "Comment_2",
    "Executing_Broker",
    "Give_Up",
    "Give_Up_Firm",
    "Buy_Quantity",
    "Sell_Quantity",
    "Account_Type_Currency",
    "Commission",
    "Clearing_Fee",
    "Exchange_Fee",
    "NFA_Fee",
    "Global_Desk_Charge",
    "RJO_Trans_Fee",
    "IB_Trans_Fee",
    "Charge_Amount_Give_Up",
    "Charge_Amount_Brokerage",
    "Charge_Amount_Other",
    "Total_Net_Charge",
    "Multiplication_Factor",
    "Market_Value",
    "Close_Price",
    "Underlying_Close_Price",
    "CUSIP_Code",
    "Commission_Account_Type",
    "Charge_Clearing_Account_Type",
    "Charge_Exchange_Account_Type",
    "Charge_NFA_Account_Type",
    "Charge_Global_Desk_Account_Type",
    "Charge_RJO_Trans_Account_Type",
    "Charge_IB_Trans_Account_Type",
    "Charge_Give_Up_Account_Type",
    "Charge_Brokerage_Account_Type",
    "Charge_Other_Account_Type",
    "Tracer_Number",
    "Order_Number",
    "Bloomberg_Root",
    "Bloomberg_Exchange",
    "Bloomberg_Market_Sector",
    "Option_Premium",
    "Security_Desc_2",
    "Security_Desc_3",
    "Trade_Desc_1",
    "Trade_Desc_2",
    "Trade_Desc_3",
    "Delta_Factor",
    "Product_Currency",
    "Commission_Field_Define_Code",
    "Charge_Clearing_Field_Define_Code",
    "Charge_Exchange_Field_Define_Code",
    "Charge_NFA_Field_Define_Code",
    "Charge_Global_Desk_Field_Define_Code",
    "Charge_RJO_Field_Define_Code",
    "Charge_IB_Trans_Field_Define_Code",
    "Charge_Give_Up_Field_Define_Code",
    "Charge_Brokerage_Field_Define_Code",
    "Charge_Other_Field_Define_Code",
    "Charge_Clearing_Field_Usage_Code",
    "Charge_Exchange_Field_Usage_Code",
    "Charge_NFA_Field_Usage_Code",
    "Charge_Global_Desk_Field_Usage_Code",
    "Charge_RJO_Field_Usage_Code",
    "Charge_IB_Trans_Field_Usage_Code",
    "Charge_Give_Up_Field_Usage_Code",
    "Charge_Brokerage_Field_Usage_Code",
    "Charge_Other_Field_Usage_Code",
    "Entry_Date",
    "Round_Half_Turn",
]

STAT_FIELDS = [
    "Record_Type",
    "Account_Number",
    "Account_Name",
    "Contract",
    "Trade_Date",
    "Security_Desc_1",
    "Quantity",
    "Commission",
    "Trade_Month",
    "Trade_Day",
]

RJO_HEADERS = [
    "Record_Type",
    "Firm_Code",
    "Office",
    "Salesman",
    "Account_Number",
    "Account_Class",
    "Account_Subclass",
    "Account_Type",
    "Card_Number",
    "Buy_Sell",
    "Quantity",
    "Security_Desc_1",
    "Formatted_Trade_Price",
    "Trade_Date",
    "Open_Close",
    "Contract_Month",
    "Contract_Day",
    "Security_Type",
    "Security_Subtype",
    "Option_Strike_Price",
    "Option_Expire_Date",
    "Subaccount_ID",
    "Broker",
    "Spread",
    "Account_Name",
    "Source_Account_Number",
    "Settlement_Date",
    "Exchange",
    "Trading_Exchange",
    "Contract",
    "Contract_Symbol",
    "Product_Type",
    "Trade_Price",
    "Trade_Calc_Control",
    "Comment_1",
    "Comment_2",
    "Executing_Broker",
    "Give_Up",
    "Give_Up_Firm",
    "Buy_Quantity",
    "Sell_Quantity",
    "Account_Type_Currency",
    "Commission",
    "Clearing_Fee",
    "Exchange_Fee",
    "NFA_Fee",
    "Global_Desk_Charge",
    "RJO_Trans_Fee",
    "IB_Trans_Fee",
    "Charge_Amount_Give_Up",
    "Charge_Amount_Brokerage",
    "Charge_Amount_Other",
    "Total_Net_Charge",
    "Multiplication_Factor",
    "Market_Value",
    "Close_Price",
    "Underlying_Close_Price",
    "CUSIP_Code",
    "Commission_Account_Type",
    "Charge_Clearing_Account_Type",
    "Charge_Exchange_Account_Type",
    "Charge_NFA_Account_Type",
    "Charge_Global_Desk_Account_Type",
    "Charge_RJO_Trans_Account_Type",
    "Charge_IB_Trans_Account_Type",
    "Charge_Give_Up_Account_Type",
    "Charge_Brokerage_Account_Type",
    "Charge_Other_Account_Type",
    "Tracer_Number",
    "Order_Number",
    "Bloomberg_Root",
    "Bloomberg_Exchange",
    "Bloomberg_Market_Sector",
    "Option_Premium",
    "Security_Desc_2",
    "Security_Desc_3",
    "Trade_Desc_1",
    "Trade_Desc_2",
    "Trade_Desc_3",
    "Delta_Factor",
    "Product_Currency",
    "Commission_Field_Define_Code",
    "Charge_Clearing_Field_Define_Code",
    "Charge_Exchange_Field_Define_Code",
    "Charge_NFA_Field_Define_Code",
    "Charge_Global_Desk_Field_Define_Code",
    "Charge_RJO_Field_Define_Code",
    "Charge_IB_Trans_Field_Define_Code",
    "Charge_Give_Up_Field_Define_Code",
    "Charge_Brokerage_Field_Define_Code",
    "Charge_Other_Field_Define_Code",
    "Charge_Clearing_Field_Usage_Code",
    "Charge_Exchange_Field_Usage_Code",
    "Charge_NFA_Field_Usage_Code",
    "Charge_Global_Desk_Field_Usage_Code",
    "Charge_RJO_Field_Usage_Code",
    "Charge_IB_Trans_Field_Usage_Code",
    "Charge_Give_Up_Field_Usage_Code",
    "Charge_Brokerage_Field_Usage_Code",
    "Charge_Other_Field_Usage_Code",
    "Entry_Date",
    "Round_Half_Turn",
]

MONTHS = [
    "None",
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
]


# Markets
NYMEX = "NYM"
ICE = "ICE"
CME = "CME"

# Fee Schedule Constants
DEFAULT_TRADE_FEE = 2.00
RJO_DEFAULT_TRADE_FEE_2024 = 0.85
ICE_TRADE_FEE = 1.85
FEE_SCHEDULE = {
    0: {
        ICE: RJO_DEFAULT_TRADE_FEE_2024,
        NYMEX: RJO_DEFAULT_TRADE_FEE_2024,
        CME: RJO_DEFAULT_TRADE_FEE_2024,
    },
    84663: {  # WRANGLER
        ICE: RJO_DEFAULT_TRADE_FEE_2024,
        NYMEX: RJO_DEFAULT_TRADE_FEE_2024,
        CME: RJO_DEFAULT_TRADE_FEE_2024,
    },
    84478: {  # IGS
        ICE: RJO_DEFAULT_TRADE_FEE_2024,
        NYMEX: RJO_DEFAULT_TRADE_FEE_2024,
        CME: RJO_DEFAULT_TRADE_FEE_2024,
    },
    85478: {  # IGS
        ICE: RJO_DEFAULT_TRADE_FEE_2024,
        NYMEX: RJO_DEFAULT_TRADE_FEE_2024,
        CME: RJO_DEFAULT_TRADE_FEE_2024,
    },
}
FEE_SCHEDULE_DEC_2021 = {
    0: {
        ICE: DEFAULT_TRADE_FEE,
        NYMEX: DEFAULT_TRADE_FEE,
        CME: DEFAULT_TRADE_FEE,
    },
    84663: {  # WRANGLER
        ICE: ICE_TRADE_FEE,
        NYMEX: ICE_TRADE_FEE,
        CME: ICE_TRADE_FEE,
    },
    84478: {  # IGS
        ICE: ICE_TRADE_FEE,
        NYMEX: DEFAULT_TRADE_FEE,
        CME: DEFAULT_TRADE_FEE,
    },
    85478: {  # IGS
        ICE: ICE_TRADE_FEE,
        NYMEX: DEFAULT_TRADE_FEE,
        CME: DEFAULT_TRADE_FEE,
    },
}
FEE_SCHEDULE_JULY_2021 = {
    0: {
        ICE: DEFAULT_TRADE_FEE,
        NYMEX: DEFAULT_TRADE_FEE,
        CME: DEFAULT_TRADE_FEE,
    },
    84663: {  # WRANGLER
        ICE: ICE_TRADE_FEE,
        NYMEX: ICE_TRADE_FEE,
        CME: ICE_TRADE_FEE,
    },
    84478: {  # IGS
        ICE: DEFAULT_TRADE_FEE,
        NYMEX: DEFAULT_TRADE_FEE,
        CME: DEFAULT_TRADE_FEE,
    },
    85478: {  # IGS
        ICE: DEFAULT_TRADE_FEE,
        NYMEX: DEFAULT_TRADE_FEE,
        CME: DEFAULT_TRADE_FEE,
    },
}
FEE_SCHEDULE_OCT_2012 = {
    0: {
        ICE: DEFAULT_TRADE_FEE,
        NYMEX: DEFAULT_TRADE_FEE,
        CME: DEFAULT_TRADE_FEE,
    },
    84663: {  # WRANGLER
        ICE: DEFAULT_TRADE_FEE,
        NYMEX: DEFAULT_TRADE_FEE,
        CME: DEFAULT_TRADE_FEE,
    },
    84478: {  # IGS
        ICE: DEFAULT_TRADE_FEE,
        NYMEX: DEFAULT_TRADE_FEE,
        CME: DEFAULT_TRADE_FEE,
    },
    85478: {  # IGS
        ICE: DEFAULT_TRADE_FEE,
        NYMEX: DEFAULT_TRADE_FEE,
        CME: DEFAULT_TRADE_FEE,
    },
}


FEE_SCHEDULE_AS_RJO_GIB = FEE_SCHEDULE
FEE_REGIMES = {
    "2010-01-01": FEE_SCHEDULE_DEC_2021,
    "2024-01-01": FEE_SCHEDULE_AS_RJO_GIB,
}

FEE_HISTORY: Dict[int, Dict[int, Dict[str, float]]] = {
    20101001: FEE_SCHEDULE_OCT_2012,
    20210701: FEE_SCHEDULE_JULY_2021,
    20211201: FEE_SCHEDULE_DEC_2021,
    20240101: FEE_SCHEDULE_AS_RJO_GIB,
}
