#!/usr/bin/env python3

import arrow
import click
from loguru import logger
import pandas as pd # noqa

from src.infrastructure.display import display_df, display_stats
from src.infrastructure.repo import get_rjo_agg_data

SUMMARY_COLS = [
    "Record_Type",
    "Record_Date",
    "Account_Number",
    "Account_Name",
    "Contract",
    "Market",
    "Trade_Date",
    "Quantity",
    "Commission",
    "Net_Commission",
]

GROUPBY_COLS = [
    "Record_Type",
    "Account_Number",
    "Account_Name",
    "Contract",
    "Market",
]

DAY_COLS = ["Trade_Date"]


def get_first_day_of_month(date):
    return date.replace(day=1)


def get_first_day_of_year(date):
    return date.replace(month=1, day=1)


def get_start_of_today(date):
    return date


span_factory = {
    "m": get_first_day_of_month,
    "y": get_first_day_of_year,
    "d": get_start_of_today,
}


def filter_trades_by_date_span(df, span, as_of_date):
    from_date = int(span_factory[span](as_of_date).format("YYYYMMDD"))
    end_date = int(as_of_date.format("YYYYMMDD"))
    filtered = df[(df["Record_Date"] >= from_date) & (df["Record_Date"] <= end_date)]
    return filtered[SUMMARY_COLS].sort_values(by=["Commission"])


def group_stats_df(df, by_day=False):
    groupby_cols = DAY_COLS + GROUPBY_COLS if by_day else GROUPBY_COLS
    grouped = df.groupby(groupby_cols).agg(
        {"Commission": ["sum"], "Quantity": ["sum"], "Net_Commission": ["sum"]}
    )
    grouped = grouped.reset_index()
    grouped.columns = grouped.columns.get_level_values(0)
    grouped = grouped[grouped["Record_Type"].isin(["A", "B", "E", "Q", "S", "T", "X"])]
    return grouped


def is_ascending(column_name):
    if column_name[0] in ["-", "+"]:
        return column_name[0] == "+"
    return True


def stats_as_of_date(year, month, day):
    if year % 4 > 0 and month == 2 and day == 29:
        day = 28
    return (
        arrow.Arrow(year, month, day)
        if day
        else arrow.Arrow(year, month, 1).shift(months=1).shift(days=-1)
    )


@click.command()
@click.option("--year", default=arrow.utcnow().year, help="year of trades you want")
@click.option("--month", default=arrow.utcnow().month, help="month of trades you want")
@click.option("--day", default=0, help="day of trades you want")
@click.option(
    "--span",
    default="d",
    type=click.Choice(["y", "m", "d"]),
    help="span of trades you want",
)
@click.option("--by_day/--sum_days", default=False, help="show individual day data")
@click.option("--sort_col", default="Commission", help="column to sort by")
@click.option(
    "--show_detail/--hide_detail",
    default=True,
    help="show the detailed trade data table",
)
@click.option(
    "--show_by_cust/--hide_by_cust",
    default=True,
    help="show customers by total commission ytd",
)
def main(year, month, day, span, by_day, sort_col, show_detail, show_by_cust):
    """
    This function is the command line interface. It is used to call the main
    function.
    """
    as_of_date = stats_as_of_date(year, month, day)
    prev_year_as_of_date = stats_as_of_date(year - 1, month, day)
    curr_year_agg_data = get_rjo_agg_data(year)
    prev_year_agg_data = get_rjo_agg_data(year - 1)
    logger.debug(f"dates: as_of_date: {as_of_date}, py_aod: {prev_year_as_of_date}")
    prev_year_to_date_data = filter_trades_by_date_span(
        prev_year_agg_data, "y", prev_year_as_of_date
    )
    curr_year_to_date_data = filter_trades_by_date_span(
        curr_year_agg_data, "y", as_of_date
    )
    display_stats(
        curr_year_to_date_data,
        f'Curr Year-to-Date as of {as_of_date.format("DD MMMM YYYY")}',
    )
    display_stats(
        prev_year_to_date_data,
        f'Prev Year-to-Date as of {prev_year_as_of_date.format("DD-MMM-YYYY")}',
    )
    if span != "y":
        month_data = filter_trades_by_date_span(curr_year_agg_data, "m", as_of_date)
        display_stats(
            month_data, f'CY Month-to-Date as of {as_of_date.format("DD MMMM YYYY")}'
        )
        prev_year_month_data = filter_trades_by_date_span(
            prev_year_to_date_data, "m", prev_year_as_of_date
        )
        display_stats(
            prev_year_month_data,
            f'PY Month-to-Date as of {prev_year_as_of_date.format("DD MMMM YYYY")}',
        )
    if span not in ["y", "m"]:
        day_data = filter_trades_by_date_span(curr_year_agg_data, "d", as_of_date)
        display_stats(day_data, f'Day as of {as_of_date.format("DD MMMM YYYY")}')
    filtered_data = filter_trades_by_date_span(curr_year_agg_data, span, as_of_date)
    display_data = group_stats_df(filtered_data, by_day)
    if show_detail:
        display_df(display_data, sort_col, "Today's Trades by Customer")
    curr_ytd_by_cust = (
        curr_year_to_date_data.groupby(["Account_Name"])[
            ["Commission", "Quantity", "Net_Commission"]
        ]
        .sum()
        .reset_index()
    )
    prev_ytd_by_cust = (
        prev_year_to_date_data.groupby(["Account_Name"])[
            ["Commission", "Quantity", "Net_Commission"]
        ]
        .sum()
        .reset_index()
        .rename(
            columns={
                "Commission": "Prev_Commission",
                "Net_Commission": "Prev_Net_Commission",
                "Quantity": "Prev_Quantity",
            }
        )
    )  # noqa: E501
    by_cust = pd.merge(
        prev_ytd_by_cust,
        curr_ytd_by_cust,
        on="Account_Name",
        how="outer",
        validate="m:m",
    ).fillna(
        0.0
    )  # noqa: E501
    if show_by_cust:
        by_cust.sort_values(by=["Net_Commission", "Prev_Net_Commission"])
        display_df(by_cust, "Net_Commission", "Previous YTD Revenue by Customer")


if __name__ == "__main__":
    logger.info("calling main()")
    main()
